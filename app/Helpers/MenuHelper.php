<?php

namespace App\Helpers;

use App\Models\Service;

class MenuHelper
{
    /**
     * Get all services for the menu
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public static function getMenuServices()
    {
        // Get top-level services that should be shown in the menu
        return Service::whereNull('parent_id')
            ->where('status', 'active')
            ->where('show_in_menu', true)
            ->orderBy('menu_order')
            ->orderBy('title')
            ->with([
                'category', // Load category relationship
                'children' => function ($query) {
                    $query->where('status', 'active')
                        ->where('show_in_menu', true)
                        ->orderBy('menu_order')
                        ->orderBy('title')
                        ->with('category'); // Load category for children too
                }
            ])
            ->get();
    }

    /**
     * Get all services for the footer's Designs section
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public static function getDesignServices()
    {
        // Get all active services for the footer
        return Service::where('status', 'active')
            ->with(['parent.category', 'category']) // Eager load parent and category relationships
            ->orderBy('title')
            ->take(15) // Limit to prevent too many items in footer
            ->get();
    }

    /**
     * Get top-level services for the footer
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public static function getTopServices()
    {
        // Get top-level active services for the footer
        return Service::whereNull('parent_id')
            ->where('status', 'active')
            ->with('category') // Load category relationship
            ->orderBy('menu_order')
            ->orderBy('title')
            ->take(6) // Limit to prevent too many items in footer
            ->get();
    }
}
