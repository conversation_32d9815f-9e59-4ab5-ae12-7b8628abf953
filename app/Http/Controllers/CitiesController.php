<?php

namespace App\Http\Controllers;

use App\Models\City;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Validator;

class CitiesController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth')->except('interiorDesignersIn');
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        $cities = City::orderBy('priority', 'desc')->orderBy('name')->paginate(10);
        return view('admin.cities.index', compact('cities'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        return view('admin.cities.create');
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:100',
            'banner' => 'nullable|image|mimes:jpeg,png,jpg,webp|max:2048',
            'priority' => 'required|integer|min:0',
            'status' => 'required|in:active,inactive',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        // Generate SEO URL
        $seoUrl = Str::slug($request->name);

        // Check if SEO URL already exists
        $count = City::where('seo_url', $seoUrl)->count();
        if ($count > 0) {
            $seoUrl = $seoUrl . '-' . ($count + 1);
        }

        // Handle banner upload
        $bannerName = null;
        if ($request->hasFile('banner')) {
            $banner = $request->file('banner');
            $bannerName = $seoUrl . '-banner-' . time() . '.' . $banner->getClientOriginalExtension();

            // Create directory if it doesn't exist
            $path = public_path('images/cities');
            if (!File::isDirectory($path)) {
                File::makeDirectory($path, 0777, true, true);
            }

            $banner->move($path, $bannerName);
        }

        // Create city
        City::create([
            'name' => $request->name,
            'seo_url' => $seoUrl,
            'banner' => $bannerName,
            'priority' => $request->priority,
            'status' => $request->status,
        ]);

        return redirect()->route('city.index')
            ->with('success', 'City created successfully.');
    }

    /**
     * Display the specified resource.
     *
     * @param  string  $id
     * @return \Illuminate\View\View
     */
    public function show(string $id)
    {
        $city = City::findOrFail($id);
        return view('admin.cities.show', compact('city'));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  string  $id
     * @return \Illuminate\View\View
     */
    public function edit(string $id)
    {
        $city = City::findOrFail($id);
        return view('admin.cities.edit', compact('city'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(Request $request, string $id)
    {
        $city = City::findOrFail($id);

        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:100',
            'banner' => 'nullable|image|mimes:jpeg,png,jpg,webp|max:2048',
            'priority' => 'required|integer|min:0',
            'status' => 'required|in:active,inactive',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        // Generate SEO URL if name has changed
        if ($city->name != $request->name) {
            $seoUrl = Str::slug($request->name);

            // Check if SEO URL already exists
            $count = City::where('seo_url', $seoUrl)
                ->where('id', '!=', $id)
                ->count();

            if ($count > 0) {
                $seoUrl = $seoUrl . '-' . ($count + 1);
            }

            $city->seo_url = $seoUrl;
        }

        // Handle banner upload
        if ($request->hasFile('banner')) {
            // Delete old banner if exists
            if ($city->banner && File::exists(public_path('images/cities/' . $city->banner))) {
                File::delete(public_path('images/cities/' . $city->banner));
            }

            $banner = $request->file('banner');
            $bannerName = $city->seo_url . '-banner-' . time() . '.' . $banner->getClientOriginalExtension();

            // Create directory if it doesn't exist
            $path = public_path('images/cities');
            if (!File::isDirectory($path)) {
                File::makeDirectory($path, 0777, true, true);
            }

            $banner->move($path, $bannerName);
            $city->banner = $bannerName;
        }

        // Update city
        $city->name = $request->name;
        $city->priority = $request->priority;
        $city->status = $request->status;
        $city->save();

        return redirect()->route('city.index')
            ->with('success', 'City updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  string  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy(string $id)
    {
        $city = City::findOrFail($id);

        // Delete banner if exists
        if ($city->banner && File::exists(public_path('images/cities/' . $city->banner))) {
            File::delete(public_path('images/cities/' . $city->banner));
        }

        $city->delete();

        return redirect()->route('city.index')
            ->with('success', 'City deleted successfully.');
    }

    /**
     * Display the interior designers in a specific city.
     *
     * @param  string  $seo_url
     * @return \Illuminate\View\View
     */
    public function interiorDesignersIn($seo_url = '')
    {
        $city = City::where('seo_url', $seo_url)->where('status', 'active')->firstOrFail();
        return view('interior_designers_in_cities', compact('city'));
    }
}
