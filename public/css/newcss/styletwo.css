Styletwo.css
/*
* Prefixed by https://autoprefixer.github.io
* PostCSS: v8.4.14,
* Autoprefixer: v10.4.7
* Browsers: last 4 version
*/

#btn-to-top,
#left,
#right,
.au-btn,
.box-head--border,
.copyright,
.date--blog3,
.foot-call,
.latest__item--content,
.menu-mobile__more,
.overlay,
.partner__item,
.see-more,
.service-3-icon,
.service-3__inner,
.social,
.statistic__item>div,
.status,
.testi .owl-theme .owl-nav div,
.testi-job em,
.testi-name,
.title,
.title-detail,
.whatsapplink1,
footer {
	text-align: center
}

.big-qoute,
.dropdown-submenu,
.form,
.image-container,
.partner-wrap1,
.section-content,
.title-2,
.under-title-2,
footer,
section {
	position: relative
}

#right,
.float-right,
.pull-right {
	float: right
}

.ShowHide,
.post-lockdown,
.post-lockdown1 {
	font-family: Didact Gothic, sans-serif
}



*,
.row#owl-testi,
body,
html {
	margin: 0;

}

#left p,
.status,
.testi-name {
	letter-spacing: 1px
}

.au-btn,
.btn-one {
	white-space: nowrap
}

.box-container ul,
footer ul {
	list-style-type: none
}

body,
html {
	font-size: 16px;
	background: #f2f2f2;
	font-family: Roboto, Arial, sans-serif
}

.header-mobile .logo-mob img {
	width: 45%;
	position: absolute
}

.header-wrap .logo img {
	width: 9%;
	position: absolute;
	/* margin-top: -10px; */
}

body,
p {
	line-height: calc(22/13);
	font-size: 14px
}

.blog-details .mostreadblog .mostreadtitle a:hover,
.nav-toggle:hover,
.read-more:hover,
.servicebtn:hover,
a:active,
a:hover,
a:link,
a:visited {
	text-decoration: none
}

.title,
h1,
h2,
h3,
h4,
h5,
h6 {
	text-transform: capitalize;
	font-family :'Silka Regular';
}

* {
	padding: 0;
	box-sizing: border-box;
	-moz-box-sizing: border-box;
	-webkit-box-sizing: border-box
}

.status {
	background: #eee;
	padding: 10px;
	margin-bottom: 0;
	color: #000
}

#left a,
.covid a {
	color: #d60000 !important
}

#left a:hover,
.covid a:hover {
	color: #d60000;
	text-decoration: none;
	font-weight: 700
}

.locations a:hover,
.mandatory {
	color: red
}

.line-input,
.modal-content {
	color: #fff;
	outline: 0
}

.modal-backdrop.show {
	opacity: 0 !important
}

.modal-open {
	overflow: hidden !important
}

#left,
.ShowHide,
.img-blog>a,
.latest__item {
	overflow: hidden
}

/* Silka Bold with custom name */
@font-face {
    font-family: 'Silka Bold';
    src: url('/fonts/silka-bold-webfont.eot'); /* IE9 Compat Modes */
    src: url('/fonts/silka-bold-webfont.eot?#iefix') format('embedded-opentype'); /* IE6-IE8 */
        
    font-weight: bold;
    font-style: normal;
}

/* Silka Regular with custom name */
@font-face {
    font-family: 'Silka Regular';
    src: url('/fonts/silka-regular-webfont.eot'); /* IE9 Compat Modes */
    src: url('/fonts/silka-regular-webfont.eot?#iefix') format('embedded-opentype'), /* IE6-IE8 */
         url('/fonts/silka-regular-webfont.woff2') format('woff2'), /* Modern browsers */
         url('/fonts/silka-regular-webfont.woff') format('woff'), /* Older browsers */
         url('/fonts/silka-regular-webfont.ttf') format('truetype'); /* Older devices */
    font-weight: normal;
    font-style: normal;
}


#contactModal .modal-content {
	-webkit-box-shadow: 0 0 50px rgba(0, 0, 0, .5) !important;
	box-shadow: 0 0 50px rgba(0, 0, 0, .5) !important;
	outline: 0
}

.modal-image {
	width: 100%;
	height: auto;
	-o-object-fit: cover;
	object-fit: cover;
	border-radius: 5px 5px 0 0;
	outline: 0
}

.modal-body {
	background-color: #976845;
	outline: 0
}

.form-group {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
	margin-bottom: 15px
}

#left p,
.authorised-partners,
nav>ul,
p,
ul {
	margin-bottom: 0
}

.form-group label {
	-webkit-box-flex: 0;
	-ms-flex: 0 0 100px;
	flex: 0 0 100px;
	margin-right: 10px
}

.box-head,
.pull-left {
	float: left
}

.modal-buttons {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-pack: end;
	-ms-flex-pack: end;
	justify-content: flex-end
}

.breadlist>ul.list-inline>li,
.contact__content,
.image-container,
.partner-wrap1 .partner__item,
.ul--inline>li {
	display: inline-block
}

.modal-buttons button {
	margin-left: 10px;
	background-color: #fff;
	color: #b02d2d;
	outline: 0;
	border: 0
}

.line-input {
	-webkit-box-flex: 1;
	-ms-flex: 1;
	flex: 1;
	border: 0;
	border-bottom: 1px solid #fff;
	background-color: transparent;
	padding: 5px;
	width: 100%
}

.line-input::-webkit-input-placeholder {
	color: #fff;
	opacity: .6
}

.line-input::-moz-placeholder {
	color: #fff;
	opacity: .6
}

.line-input:-ms-input-placeholder {
	color: #fff;
	opacity: .6
}

.line-input::-ms-input-placeholder {
	color: #fff;
	opacity: .6
}

.line-input::placeholder {
	color: #fff;
	opacity: .6
}

.centered-button {
	position: absolute;
	top: 50%;
	left: 50%;
	-webkit-transform: translate(-50%, -50%);
	-ms-transform: translate(-50%, -50%);
	transform: translate(-50%, -50%)
}

.form-check-input {
	border: 2px solid #ccc !important;
	padding: 6px !important
}

/*.card-body {*/
/*	margin-top: 4rem !important*/
/*}*/

.ShowHide {
	background-color: #d1d1d1;
	padding: 10px;
	color: #000
}

#left {
	height: 50px
}

#right {
	width: 30px;
	height: 20px
}

#right a:hover {
	color: #000;
	text-decoration: none;
	font-weight: 600
}

.add-background .col-md-12,
.add-background .row,
.post-lockdown .col-md-12,
.post-lockdown .row {
	padding: 0;
	margin: 0
}

.post-lockdown1,
.post-lockdown2,
.post-lockdown3,
.post-lockdown4,
.post-lockdown5,
.post-lockdown6 {
	padding: 20px
}

.post-lockdown1 img {
	width: 400px
}

.lockdowndata {
	padding: 50px
}

.lockdowndata h2.mainheading {
	font-size: 25px;
	text-transform: capitalize;
	font-weight: 700
}

.lockdowndata p.subheading {
	font-size: 23px;
	font-weight: 700
}

.lockdowndata p.subheading span,
.mediacontent a,
.mediacontent p span.clink {
	color: #00f
}

.lockdowndata p.data {
	font-size: 20px
}

.blog-details .social-share ul.list li a,
.lockdowndata p.data span {
	color: #d60000
}

.lockdowndata .border1 {
	border-bottom: 10px solid #d60000 !important;
	width: 86px;
	margin-bottom: 20px;
	margin-left: 0;
	margin-top: 0;
	border-radius: 10px
}

body {
	font-weight: 400;
	color: #555;
	-webkit-font-smoothing: antialiased;
	-webkit-text-size-adjust: 100%
}

img {
	max-width: 100%;
	height: auto
}

#filter .active span,
.blog-details ul li a:hover,
.copyright span,
.draw a,
.mediacontent a:hover,
.pos-resize h1:hover,
.pos-resize h2:hover,
.title>span,
a:active,
a:hover {
	color: #C7A45F
}

.blog-details .blog-content p,
.blog-details .mostreadblog .mostreadtitle a,
.gray-shedow-blog a:hover ul li,
.rep-left-blog ul li .fa:hover,
.rep-left-blog ul li:hover,
a:link,
a:visited {
	color: #000
}
.ng-binding {
    font-family :'Silka Regular';
}

.latest__item--inner a:hover,
.ng-binding:hover,
.service__item-inner a:hover,
.top-contact--left>span>a:hover,
ul>li>a:hover {
	text-decoration: none;
	color: #C7A45F;
	font-family :'Silka Regular';
}

.ul--no-style>li {
	list-style: none
}

.dropdown-submenu:hover .dropdown-menu-1,
.dropdown:hover .dropdown-menu,
.img-blog>a,
.menu-desktop--show,
.no-js .owl-carousel,
.owl-carousel.owl-loaded,
.slick-initialized .slick-slide,
.slick-slide img,
.visible,
footer img {
	display: block
}

h1,
h2,
h3,
h4,
h5,
h6 {
	color: #333;
	margin-bottom: 0;
	font-weight: 700;
	line-height: calc(24/18)
}

.title--36,
h1 {
	font-size: 36px
}

.title-small,
h2 {
	font-size: 30px
}

h3 {
	font-size: 24px
}

h4 {
	font-size: 18px
}

.blog-item p,
.box-container ul,
a,
h5,
nav>ul>li>a {
	font-size: 14px;
}

.title,
.title>span {
	font-size: 22px !important;
	font-weight: 700;
}

.blog-details .blog-content h2,
.breadlist ul li,
.breadlist ul li a,
.cat-name a em,
.copyright,
.date .date__inner span,
h6,
nav.menu-mobile>ul>li>ul>li>ul>li a {
	font-size: 14px
}
.blog-details .blog-content h2{
    font-size: 22px !important;
}
.m-t-60 {
	margin-top: 18px
}

.m-t-20,
footer .social {
	margin-top: 20px
}

.img-blog,
.m-b-20 {
	margin-bottom: 20px
}

.p-r-15 {
	padding-right: 15px
}

.p-l-15 {
	padding-left: 15px
}

.title {
	margin-top: 30px;
}
a {
    font-family: 'Silka Regular';
}

.my-float,
.owl-theme .owl-nav,
.title-detail,
/* header .logo, */
header h1 {
	margin-top: 10px
}

.title-2 {
	font-size: 26px;
	margin-bottom: 1%;
	position: relative
}

.under-title-2 {
	display: block;
	height: 12px;
	width: 12px;
	background: #ebcd1e;
	bottom: -19px;
	left: 50%;
	margin-left: -9px
}

.overlay,
nav.menu-mobile {
	left: 0;
	position: absolute
}

.under-title-2:after,
.under-title-2:before {
	position: absolute;
	height: 1px;
	width: 100px;
	top: 5px;
	background: rgba(0, 0, 0, .34);
	content: ''
}

.under-title-2:before {
	left: -111px
}

.under-title-2:after {
	left: 23px
}

.nav-toggle,
.read-more {
	color: #fff !important;
	background-color: #2A2B51;
	padding: 7px 15px;
	border-radius: 10px
}

.shade-now {
	padding-bottom: 20px !important
}

.title-detail {
	margin-bottom: 2%;
	padding: 0 20px;
	font-size: 16px;
	line-height: 22px;
	font-family :'Silka Regular';
}

.owl-theme .owl-dots .owl-dot span {
	width: 11px;
	height: 11px;
	background: 0;
	border: 2px solid #666;
	-webkit-transition: .3s, -webkit-transform .3s;
	-o-transition: .3s, -o-transform .3s;
	transition: .3s, transform .3s, -webkit-transform .3s
}

.owl-theme .owl-dots .owl-dot.active span,
.owl-theme .owl-dots .owl-dot:hover span {
	background-color: #ebcd1e;
	display: block;
	border-radius: 50%;
	border: 1px solid #fff;
	-webkit-box-shadow: 0 0 0 2px #ebcd1e;
	box-shadow: 0 0 0 2px #ebcd1e
}

.box-head--border {
	width: 68px;
	line-height: 64px
}

.box-body {
	margin-left: 60px;
	padding: 0 32px
}

.au-btn {
	display: inline-block;
	line-height: 50px;
	padding: 0 15px;
	-webkit-transition: .5s;
	-o-transition: .5s;
	transition: .5s
}

.au-btn--pill {
	border-radius: 20px
}

.au-btn--yellow {
	color: #fff;
	background-color: #2A2B51
}

.au-btn--yellow:hover {
	color: #fff;
	background-color: #333
}

a.au-btn--dark {
	background-color: #000;
	color: #fff
}

a.au-btn--dark:hover {
	background-color: #fff;
	color: #ebcd1e
}

a.au-btn--white {
	color: #fff;
	font-weight: 600;
	font-size: 17px
}

.au-btn--big {
	padding: 0 50px
}

.au-btn--pill.au-btn--slide {
	border-radius: 40px
}

.au-btn--big.au-btn--pill {
	border-radius: 50px
}

#btn-to-top {
    text-align: center;
	width: 40px;
	height: 40px;
	background-color: #2A2B51;
	position: fixed;
	bottom: 10px !important;
	right: 10px !important;
	left: 20px;
	line-height: 40px;
	z-index: 1000;
	border-radius: 50%
}

#btn-to-top:hover {
	background-color: #333;
	text-decoration: none
}

#btn-to-top .fa.fa-chevron-up {
	font-weight: 400;
	color: #fff;
	font-size: 1.4rem;
	margin-top: .4rem
}

.overlay {
	background: rgba(0, 0, 0, .7);
	display: block;
	top: 0;
	width: 100%;
	height: 100%;
	-webkit-transition: .4s;
	-o-transition: .4s;
	transition: .4s
}

.parallax,
.service__item {
	background-repeat: no-repeat
}

.overlay--p-15 {
	padding: 15px
}

.overlay--invisible {
	opacity: 0
}

.overlay--border {
	height: 100%;
	width: 100%;
	border: 2px dashed #C7A45F
}

.parallax {
	background-position: center left;
	background-size: cover
}

.parallax--footer {
	padding: 40px 0;
	-webkit-box-shadow: 2px 3px 5px 2px #989a8b;
	box-shadow: 2px 3px 5px 2px #989a8b;
	margin-bottom: -50px
}

/* header .logo-mob {
	margin-top: 22px
} */

nav>ul>li {
	padding: 20px 10px
}

nav>ul>li>a {
    font-size: 14px;
}

#other,
.hidden,
.menu-desktop--hidden,
.owl-carousel .owl-dots.disabled,
.owl-carousel .owl-nav.disabled,
.slick-arrow.slick-hidden,
.slick-slide.slick-loading img,
header.header-mobile {
	display: none
}

.menu-mobile__button,
.social__item {
	display: inline-block;
	text-align: center;
	line-height: 30px
}

.top-contact {
	background: #1b375d;
	padding: 12px 0;
}

.top-contact span a {
	color: #fff;
}

.top-contact--left>span {
	color: #fff;
	font-size: 16px
}

.top-contact--left>span:first-child {
	margin-right: 5rem;
	color: #fff
}

.top-contact--right a {
	margin-right: 20px;
	font-size: 14px
}

.social .social__item:last-child,
.top-contact--right a:last-child {
	margin-right: 0
}
.silka-regular {
    font-family :'Silka Regular';
}
.silka-bold {
    font-weight : 600;
}
.blog-details h6 a:hover,
.blog-title>a:hover,
.box-container h4>a:hover,
.copyright span a:hover,
.gray-shedow-blog a:hover,
.mediaroom-content a:hover,
.top-contact--right a i:hover,
nav>ul>li>a:hover,
nav>ul>li>ul>li>a:hover,
nav>ul>li>ul>li>ul>li>a:hover {
	color: #C7A45F;
	text-decoration: none;
	 font-family: 'Silka Regular';
}

.menu-mobile__button:hover i,
.social__item:hover>i {
	color: #333
}

.top-contact--right a i {
	color: #fff;
	font-size: 16px
}

.header3 {
	z-index: 1;
	width: 100%;
	 font-family: 'Silka Regular';
}

.header-wrap {
	background: #fff;
	padding: 0 20px;
	-webkit-box-shadow: 1px 0 3px #222;
	box-shadow: 1px 0 3px #222
}

.social {
	margin-top: 55px
}

.social__item {
	-webkit-transition: .6s;
	-o-transition: .6s;
	transition: .6s;
	width: 30px;
	background: #2A2B51;
	margin-right: 7px
}

.menu-mobile__button:hover,
.social__item:hover {
	background: #595875
}

.social__item>i {
	color: #ffffff;
	font-size: 20px
}

.menu-mobile__button {
	float: right;
	width: 40px;
	height: 30px;
	border: 1px solid #c6c6c6;
	cursor: pointer;
	margin: 2.2rem 0 20px;
	background: #2A2B51
}

nav.menu-mobile li,
nav.menu-mobile>ul>li {
	border-bottom: 1px solid rgba(0, 0, 0, .05)
}

.menu-mobile__button i {
	font-size: 18px;
	margin-top: 5px;
	color: #fff;
}

.menu-mobile__more {
	position: absolute;
	top: 0;
	right: 0;
	line-height: 40px;
	padding: 15px 20px;
	cursor: pointer;
	color: #444
}

nav.menu-mobile {
	top: 77px;
	float: left;
	z-index: 50000;
	background: #fff;
	width: 100%;
	margin-right: 0
}

nav.menu-mobile>ul {
	border-top: 3px solid #2A2B51
}

nav.menu-mobile>ul>li {
	position: relative;
	padding: 0
}

.mouse,
.mouse__wheel {
	position: absolute;
	left: 50%
}

nav.menu-mobile>ul>li a {
	padding: 0 20px;
	line-height: 40px;
	display: block;
	font-family: 'Silka Regular';
}

nav.menu-mobile>ul>li a:hover {
	background-color: #ebcd1e;
	color: #444;
	 font-family: 'Silka Regular';
}

nav.menu-mobile>ul>li>ul {
	background: rgba(0, 0, 0, .03);
	 font-family: 'Silka Regular';
}

.dropdown-submenu .dropdown-menu {
	top: 0;
	left: 100%;
	margin-top: -1px
}

.dropdown-menu-1 {
	position: absolute;
	top: 0;
	left: 99%;
	z-index: 1000;
	display: none;
	float: left;
	min-width: 160px;
	padding: 5px 0;
	margin: 2px 0 0;
	font-size: 14px;
	text-align: left;
	list-style: none;
	background-color: #fff;
	-webkit-background-clip: padding-box;
	background-clip: padding-box;
	border: 1px solid rgba(0, 0, 0, .15);
	border-radius: 4px;
	-webkit-box-shadow: 0 6px 12px rgba(0, 0, 0, .175);
	box-shadow: 0 6px 12px rgba(0, 0, 0, .175)
}

.header-mobile ul li .click-on-2 {
	width: 53%;
	background: #2A2B51;
	color: #fff;
	margin-left: 6%;
	margin-top: 3%;
	margin-bottom: 3%;
	padding: 0 !important;
	text-align: center;
	font-size: 16px;
	 font-family: 'Silka Regular';
}

.header-mobile ul li .click-on-2wp {
	width: 53%;
	background: #25d366;
	color: #000;
	margin-left: 6%;
	margin-top: 1%;
	margin-bottom: 1%;
	padding: 12 0 !important;
	text-align: center;
	font-size: 16px
}

.btn-one,
.service__item h2 a:hover,
.servicebtn,
header .click-on,
header .click-onwp {
	color: #fff !important
}

.dropdown-menu li {
	text-align: center;
	padding: 3% 0
}

header .click-on {
	background: #2A2B51;
	padding: 10px 19px;
	font-size: 14px;
	font-weight: 600;
	border-radius: 20px
}

header .click-onwp {
	background: #25d366;
	padding: 10px 15px;
	font-size: 20px !important;
	border-radius: 50px
}

.service-wrap {
	display: table;
	width: 100%
}

.service__item {
	text-align: center;
	background-size: cover;
	width: 25%;
	float: left;
	display: table-cell;
	vertical-align: middle;
	min-height: 315px;
	position: relative;
	border: 1px solid #7f7f7f;
	border-right: 0
}

#service-intro,
#service-intro1,
#service-intro2,
#service-intro3 {
	background-repeat: no-repeat;
	background-size: cover
}

.service__item-inner {
	position: absolute;
	width: 78%;
	top: 50%;
	left: 50%;
	-webkit-transform: translate(-50%, -50%);
	-ms-transform: translate(-50%, -50%);
	transform: translate(-50%, -50%)
}

.img-blog>a,
.latest__item {
	position: relative
}

a.bt-oneee {
    cursor: pointer !important;
}


.service__intro h2>span {
	color: #C7A45F;
	font-size: 24px;
	font-weight: 600;
}

.page-item .page-link:hover,
.service__item h2 a,
footer ul li a:hover {
	color: #C7A45F !important
}
h2 {
    font-family: 'Silka Bold';
}
.service__intro h2 {
	font-weight: 600;
	color: #fff;
	line-height: 30px;
	text-align: center
}

.service__intro p {
	font-weight: 700;
	font-size: 16px;
	color: #fff;
	margin-top: 18px;
	margin-bottom: 38px;
	font-family :'Silka Regular';
	text-align: center
}

.service__intro {
	text-align: left;
	padding: 0 60px
}

.service__item h2 {
	margin-top: 50px
}

.service__item h2 a {
	font-size: 18px;
	font-weight: 600;
}

#service-intro {
	background-image: url('../../images/services-02.webp');
		opacity:1;
}

#service-intro1,
#service-intro2,
#service-intro3 {
	border: 2px solid #7f7f7f;
	background-position: center
}

#service-intro1 {
	background-image: url('../../images/service-011.webp');
	opacity:1;
}

#service-intro2 {
	background-image: url('../../images/service-033.webp');
		opacity:1;
}

#service-intro3 {
	background-image: url('../../images/service-044.webp');
		opacity:1;
}

#owl-partner-1,
#owl-testi {
	-ms-touch-action: auto;
	touch-action: auto
}

.parallax-testi {
	padding: 40px 0 20px;
	background-position: center
}

.contect-1,
.project-container,
.testi {
	margin-bottom: 2%
}

.testi__item {
	text-align: justify;
	-moz-text-align-last: center;
	text-align-last: center;
	margin-top: 40px
}

.testi__item>blockquote {
	color: #000;
	line-height: 22px;
	position: relative;
	width: 70%;
	margin: 0 auto;
	font-size: 14px
}

.testi-name {
	color: #000;
	font-size: 16px;
	font-weight: 400;
	margin-top: 15px
}

.testi-job em {
	font-size: 14px;
	color: #000;
	letter-spacing: 2px
}

.fa.fa-quote-right.big-qoute {
	left: 10px
}

.fa.fa-quote-left.big-qoute {
	left: -10px
}

.testi .owl-theme .owl-nav div {
	width: 50px;
	height: 50px;
	line-height: 50px;
	position: absolute;
	top: 50%;
	margin-top: -25px;
	background: #ebcd1e;
	cursor: pointer;
	-webkit-transition: .6s 0;
	-o-transition: .6s 0;
	transition: .6s 0;
	color: #fff;
	border-radius: 0;
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-pack: center;
	-ms-flex-pack: center;
	justify-content: center;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center
}

.testi .owl-theme .owl-nav div:hover {
	color: #333;
	background: #fff
}

.testi .owl-theme .owl-nav .owl-prev {
	left: 54px
}

.testi .owl-theme .owl-nav .owl-next {
	right: 54px
}

.testi .owl-carousel {
	position: static
}

.carousel-caption h1:hover,
.carousel-caption h2:hover,
.carousel-caption p:hover,
.checked {
	color: #C7A45F
}

.latest-project {
	padding-top: .5%
}

.latest__item {
	border: 2px solid #f1f1f1
}

.latest__item--content {
	position: absolute;
	top: 50%;
	left: 50%;
	-webkit-transform: translate(-50%, -50%);
	-ms-transform: translate(-50%, -50%);
	transform: translate(-50%, -50%)
}

.latest__item--inner {
	opacity: 0;
	-webkit-transition: .3s;
	-o-transition: .3s;
	transition: .3s
}

.latest__item img {
	-webkit-transition: .6s;
	-o-transition: .6s;
	transition: .6s;
	width: 100%;
	height: 248px
}

.img-blog>a>img:hover,
.latest__item:hover img {
	-webkit-transform: scale(1.1);
	-ms-transform: scale(1.1);
	transform: scale(1.1)
}

.contact-content .fa,
.latest__item--inner h3 a {
	color: #C7A45F;
	font-size: 18px
}

.cat-name a:hover em,
.latest__item--inner h3 a:hover {
	color: #fff
}

.cat-name a em {
	color: #d5d5d5
}

.blog {
	padding: 1% 0 0;
	margin-top: 2%
}

.img-blog>a>img {
	width: 100%;
	-webkit-transition: 1s;
	-o-transition: 1s;
	transition: 1s;
	height: 15rem
}

.date {
	height: 47px;
	width: 47px;
	background: #333;
	padding: 5px;
	position: absolute;
	top: 10px;
	right: 10px
}

.date--big {
	width: 80px;
	height: 80px;
	top: 20px;
	right: 20px
}

.date .date__inner,
.date--big .date__inner {
	width: 100%;
	height: 100%;
	text-transform: uppercase;
	border: 1px solid #999;
	text-align: center
}

.date .date__inner {
	padding: 7px 0
}

.blog-title>a,
.date--big .date__inner span,
.term-condition h3 {
	font-size: 18px
}

.date--big .date__inner {
	padding: 15px 0
}

.copyright .first-1,
footer .social,
footer img {
	text-align: left
}

.blog-title {
	margin-bottom: 3px;
	margin-top: -6px
}

.blog-title>a {
	text-transform: capitalize !important;
	color: #333;
	font-weight: 700;
	text-decoration: none;
	outline: 0;
	-webkit-transition: .3s;
	-o-transition: .3s;
	transition: .3s
}

.carousel .overview b,
.carousel-caption p,
.contact__content,
.date--blog3 .month,
.head,
.label,
h2 {
	text-transform: capitalize
}


footer img {
	margin: -5% 0 3%;
	width: 35%
}

.blog-details h6,
footer p {
	margin-bottom: 1%
}

.first-1 {
    font-family: 'Silka Bold';
}
footer h1,
footer h3,
footer p {
	color: #000;
	text-align: left;
}

footer ul li {
	padding: 1% 0;
	font-family: 'Silka Regular';
}

footer p a,
footer ul li a {
	color: #000 !important;
	font-size: 14px
}

footer h1,
footer h3 {
	font-size: 16px;
	padding-bottom: 4%;
	font-family: 'Silka Bold';
}

.au-btn--submit {
	outline: 0;
	border: 0;
	cursor: pointer;
	line-height: 40px;
	border-top-right-radius: 30px;
	border-bottom-right-radius: 30px;
	padding: 0 22px;
	margin-left: -5px
}

#owl-partner-1 .owl-stage {
    margin: 0 auto;
}

.social__item--circle {
	border-radius: 50%
}

.copyright {
	padding: 15px 0;
	background: #111;
	color: #fff
}

.copyright span a {
	font-size: 14px;
	color: #fff;
}

.cus_ft {
	font-size: 14px;
	color: #fff !important;
	font-family :'Silka Regular' !important;
	text-decoration: none !important;
}

.cus_ft:hover {
	color: #f47e45 !important;
}

.copyright .first-2,
.we-are__left>div {
	text-align: left;
	font-family :'Silka Regular';
}

.ft_class {
    color: #fff !important;
    text-decoration: none !important;
    font-size: 14px;
}

.pad {
    padding: 0 1%
}

.copy_ft {
    color: #fff !important;
    font-size: 14px !important;
}

.cus_mb {
    margin-bottom: 0;
}

.cus_ali {
    align-content: center;
}

.copyright .set {
	padding: 0 2%
}

.foot-call {
	float: left;
	position: fixed;
	width: 100%;
	background: #f47e45;
	opacity: 1;
	bottom: 0;
	padding: 2% 1%;
	z-index: 9999999999
}

.close,
.why-choose__inner .box-head--right,
[dir=rtl] .slick-slide {
	float: right
}

.whatsapplink1 {
	position: fixed;
	width: 50px;
	height: 50px;
	bottom: 30%;
	right: 25px;
	background-color: #25d366;
	color: #fff !important;
	border-radius: 50px;
	font-size: 30px;
	-webkit-box-shadow: 2px 2px 3px #999;
	box-shadow: 2px 2px 3px #999;
	z-index: 100
}

.modal {
	display: none;
	position: fixed;
	z-index: 1;
	padding-top: 100px;
	left: 0;
	top: 0;
	width: 100%;
	height: 100%;
	overflow: auto;
	background-color: rgba(0, 0, 0, .4)
}

.p-btn:after,
.partner__item {
	display: block
}

.modal-content {
	background-color: #fefefe;
	margin: auto;
	padding: 20px;
	border: 1px solid #888;
	width: 100% !important;
}

.our-process,
body.box {
	background-color: #fff
}

.close {
	color: #aaa;
	font-size: 28px;
	font-weight: 700
}

.num,
a.sbold {
	font-weight: 600;

}

.close:focus,
.close:hover {
	color: #000;
	text-decoration: none;
	cursor: pointer
}

.whatsapplink1 img {
	width: 20%
}

footer .col-xs-5 {
	width: 46%
}

footer .col-xs-2 {
	width: 0
}

.footerDivider {
	position: relative;
	margin: 0;
	border-right: 1px solid #fff
}

.foot-call img {
	width: 20px;
	margin-right: 2%
}

.foot-call a {
	font-size: 16px;
	color: #fff
}

.our-process {
	margin-top: 0;
	padding: 2% 0
}

.our-process .box-head img {
	width: 40px;
	height: 40px
}

.why-choose {
	padding: 90px 0 120px
}

.why-choose .title-3 {
	margin-bottom: 56px
}

.why-choose__inner .box-head {
	width: 88px
}

.why-choose__inner .box-head--border {
	border: 3px solid #bbb
}

.why-choose__inner .box-head--border i {
	color: #333;
	font-size: 36px;
	line-height: 82px
}

.why-choose__inner .box-body h3 {
	margin-bottom: 7px;
	line-height: 1
}

.why-choose__inner .box-body p {
	line-height: 20px;
	font-size: 16px;
	padding-top: 6%;
	font-weight: 700;

}

.why-choose__inner .box-body--right {
	margin-left: 88px;
	padding-left: 30px;
	padding-right: 0
}

.why-choose__inner .box-body--left {
	text-align: right;
	margin: 0 88px 0 0;
	padding-right: 30px;
	padding-left: 0
}

.date--blog3 .day,
.date--blog3 .month {
	color: #f2f2f2;
	font-size: 18px
}

.blog-item-3 {
	margin-top: 35px;
	margin-bottom: 12%
}

.img-blog-3 {
	position: relative;
	overflow: hidden
}

.date--blog3,
.label {
	position: absolute;
	z-index: 10
}

.date--blog3 {
	left: -50%;
	margin-left: 15px;
	margin-top: -15px;
	top: 50%;
	width: 100%;
	height: 30px;
	-webkit-transform: rotate(-90deg);
	-ms-transform: rotate(-90deg);
	transform: rotate(-90deg)
}

.blog-title--normal a {
	font-weight: 400
}

.blog-title.m-b-20 {
	margin-bottom: 10px
}

.contact__content {
	padding-top: 1%;
	font-size: 18px;
	color: #333;
	font-weight: 600;
	margin-left: 15%
}

.contact__inner .au-btn--big {
	padding: 0 40px;
	margin-left: 15%
}

.parallax--contact {
	padding: 35px 0
}

.we-are .see-more.see-more--left {
	margin-top: 33px
}

.overlay--light {
	background-color: rgba(0, 0, 0, .5)
}

.latest__item .overlay i {
	-webkit-transform: scale(0);
	-ms-transform: scale(0);
	transform: scale(0)
}

.latest__item:hover .overlay i {
	-webkit-transform: scale(1);
	-ms-transform: scale(1);
	transform: scale(1)
}

.partner-wrap1 .owl-stage-outer {
	padding: 41px 24px;
	border: 1px solid #f8f8f8
}

.partner__item>img {
	margin: 0 auto
}

.head,
.num {
	line-height: 1
}

.num {
	font-size: 49px;
	color: #ebcd1e
}

.head {
	font-weight: 700;
	font-size: 28px;
	color: #444;
	margin-bottom: 16px;
	margin-top: 15px
}

.p-btn {
	width: 100%;
	height: 100%;
	background: rgba(34, 34, 34, .84);
	border: 1px solid #ebebeb;
	-webkit-transition: .6s;
	-o-transition: .6s;
	transition: .6s;
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-pack: center;
	-ms-flex-pack: center;
	justify-content: center;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center
}

.p-btn:after {
	content: '';
	padding-bottom: 100%
}

body.box {
	max-width: 1200px;
	margin: auto;
	-webkit-box-shadow: 1px 1.73px 14.56px 1.44px rgba(0, 0, 0, .063);
	box-shadow: 1px 1.73px 14.56px 1.44px rgba(0, 0, 0, .063);
	overflow-x: hidden
}

.blog,
.now-use,
.shade-now {
	-webkit-box-shadow: 2px 3px 12px 0 #d0d2d5;
	box-shadow: 2px 3px 12px 0 #d0d2d5
}

.label {
	line-height: 20px;
	font-size: 18px
}

input:focus,
textarea:focus {
	border-color: black !important
}

.blog-details ul li,
.box-container,
.eminence-content p {
	padding-bottom: 1%
}

.eminence-content ul {
	color: #000;
	font-size: 14px;
	word-spacing: 2px;
	padding-left: 2%;
	padding-bottom: 1%
}

.eminence-content strong {
	font-size: 16px;
	color: #000;
	line-height: 34px
}

.half {
	width: 50%;
	float: left
}

.mediaroom-img1 img {
	width: 355px;
	height: 266px
}

.mediaroom-content p.mediaroom-info {
	text-align: justify;
	margin-right: 10px
}

@media(max-width:1200px) {
	nav.menu-desktop {
		margin-right: 0
	}

	.header-wrap {
		padding: 0 20px
	}

	.blog-item .blog-content .blog-meta {
		display: block
	}

	.header-mobile .logo-mob img {
		width: 45%;
		position: absolute;
		margin-top: 10px;
	}

	footer img {
		width: 50%
	}
}

@media(max-width:1199px) {
	.header-desktop {
		display: none
	}

	header.header-mobile {
		display: block
	}

	.header-mobile .logo-mob img {
		width: 10%;
		position: absolute;
		margin-top: 10px;
	}

	footer img {
		width: 50%
	}
}

@media(max-width:1024px) {
	.header-desktop {
		display: none
	}

	header.header-mobile {
		display: block
	}

	.testi .owl-theme .owl-nav .owl-prev {
		left: 20px
	}

	.testi .owl-theme .owl-nav .owl-next {
		right: 20px
	}

	.contact__content {
		margin-right: 17%
	}

	.header-mobile .logo-mob img {
		width: 13%;
		position: absolute;
		margin-top: 10px;
	}

	footer img {
		width: 50%
	}
}

@media(max-width:992px) {

	.contact__inner,
	.we-are__left>div {
		text-align: center
	}

	.header-desktop {
		display: none
	}

	.title-2 {
		font-size: 30px
	}

	header.header-mobile {
		display: block
	}

	.service__item,
	footer img {
		width: 50%
	}

	.contact__content {
		margin: 0 auto
	}

	.contact__inner>a {
		margin-top: 30px
	}

	.header-mobile .logo-mob img {
		width: 16%;
		position: absolute;
		margin-top: 10px;
	}

	.we-are__left {
		margin-bottom: 50px
	}
}

@media(min-width:1200px) {
	.container {
		max-width: 100%;
		padding: 0 20px
	}

	.header-mobile .logo-mob img {
		width: 10%;
		position: absolute;
		margin-top: 10px;
	}

	footer img {
		width: 60%
	}
}

@media(max-width:768px) {
	.num {
		font-size: 50px
	}

	.head {
		font-size: 30px
	}


	footer img {
		width: 55%
	}

	#left {
		height: 70px
	}
}

@media(max-width:576px) {
	.logo {
		text-align: center;
		margin-top: 2%
	}

	.testi__item blockquote>i {
		left: -30px
	}

	.service__item {
		display: block;
		width: 100%
	}

	.top-contact {
		display: none
	}

	.why-choose__inner .box-body h3 {
		font-size: 20px
	}


	footer img {
		width: 30%
	}
}

@media (min-width: 300px) and (max-width: 374px) {
    .header-mobile .logo-mob img {
		width: 37%;
		position: absolute;
		margin-top: 10px;
	}

}

@media (min-width: 375px) and (max-width: 424px) {
    .header-mobile .logo-mob img {
		width: 30%;
		position: absolute;
		margin-top: 10px;
	}

}

@media (min-width: 425px) and (max-width: 574px) {
    .header-mobile .logo-mob img {
		width: 30%;
		position: absolute;
		margin-top: 10px;
	}

}

@media (min-width: 575px) and (max-width: 767px) {
    .header-mobile .logo-mob img {
		width: 25%;
		position: absolute;
		margin-top: 10px;
	}

}

@media(max-width:425px) {
	.num {
		font-size: 40px
	}

	.head {
		font-size: 20px
	}

	.testi__item>blockquote {
		width: 70%
	}

	footer img {
		width: 60%
	}
}

.owl-carousel,
.owl-carousel .owl-item {
	-webkit-tap-highlight-color: transparent;
	position: relative
}

.owl-carousel {
	display: none;
	width: 100%;
	z-index: 1
}

.owl-carousel .owl-stage {
	position: relative;
	-ms-touch-action: pan-Y;
	-moz-backface-visibility: hidden
}

.owl-carousel .owl-stage:after {
	content: ".";
	display: block;
	clear: both;
	visibility: hidden;
	line-height: 0;
	height: 0
}

.owl-carousel .owl-stage-outer {
	position: relative;
	overflow: hidden;
	-webkit-transform: translate3d(0, 0, 0)
}

.owl-carousel .owl-item {
	min-height: 1px;
	float: left
}

.owl-carousel .owl-item img {
	display: block;
	width: 100%
}

.owl-carousel .owl-dot,
.owl-carousel .owl-nav .owl-next,
.owl-carousel .owl-nav .owl-prev {
	cursor: pointer;
	-webkit-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none
}

.owl-carousel.owl-loading {
	opacity: 0;
	display: block
}

.owl-carousel.owl-refresh .owl-item,
.slick-loading .slick-slide,
.slick-loading .slick-track {
	visibility: hidden
}

.owl-carousel.owl-drag .owl-item {
	-webkit-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none
}

.owl-theme .owl-dots,
.owl-theme .owl-nav {
	text-align: center;
	-webkit-tap-highlight-color: transparent
}

.owl-theme .owl-nav [class*=owl-] {
	color: #fff;
	font-size: 14px;
	margin: 5px;
	padding: 4px 7px;
	background: #d6d6d6;
	display: inline-block;
	cursor: pointer;
	border-radius: 3px
}

.slick-list,
.slick-slider {
	position: relative;
	display: block
}

.slick-slider {
	-webkit-box-sizing: border-box;
	box-sizing: border-box;
	-webkit-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;
	-webkit-touch-callout: none;
	-khtml-user-select: none;
	-ms-touch-action: pan-y;
	touch-action: pan-y;
	-webkit-tap-highlight-color: transparent
}

.slick-list {
	overflow: hidden;
	margin: 0;
	padding: 0
}

.counter-top-area,
.counter-top-area1,
.counter-top-areaHowItWork {
	-webkit-transition: .3s 0;
	-o-transition: .3s 0;
	transition: .3s 0;
	position: relative;
	margin-bottom: 3%;
	margin-top: 4%
}

.btn.focus,
.btn:focus,
.slick-list:focus,
button:focus,
input:focus {
	outline: 0
}

.slick-list.dragging {
	cursor: pointer
}

.slick-slider .slick-list,
.slick-slider .slick-track {
	-webkit-transform: translate3d(0, 0, 0);
	-ms-transform: translate3d(0, 0, 0);
	transform: translate3d(0, 0, 0)
}

.slick-track {
	position: relative;
	top: 0;
	left: 0;
	display: block;
	margin-left: auto;
	margin-right: auto
}

.slick-track:after,
.slick-track:before {
	display: table;
	content: ''
}

.slick-track:after {
	clear: both
}

.slick-slide {
	display: none;
	float: left;
	height: 100%;
	min-height: 1px
}

.slick-slide.dragging img {
	pointer-events: none
}

.slick-vertical .slick-slide {
	display: block;
	height: auto;
	border: 1px solid transparent
}

@font-face {
	font-family: FontAwesome;
	src: url('../../fonts/fontawesome-webfont.eot?v=4.7.0');
	src: url('../../fonts/fontawesome-webfont.woff2?v=4.7.0') format('woff2');
	font-weight: 400;
	font-style: normal
}

.fa {
	display: inline-block;
	font: 14px/1 FontAwesome;
	font-size: inherit;
	text-rendering: auto;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale
}

.fa-lg {
	font-size: 1.33333333em;
	line-height: .75em;
	vertical-align: -15%
}

.fa-2x {
	font-size: 2em
}

.fa-3x {
	font-size: 3em
}

.fa-4x {
	font-size: 4em
}

.fa-5x {
	font-size: 5em
}

.fa-whatsapp:before {
	content: "\f232"
}

.fa-phone:before {
	content: "\f095"
}

.fa-file:before {
	content: "\f15b"
}

.fa-envelope:before {
	content: "\f0e0"
}

.fa-linkedin:before {
	content: "\f0e1"
}

.fa-twitter:before {
	content: "\f099"
}

.fa-facebook-f:before,
.fa-facebook:before {
	content: "\f09a"
}

.fa-instagram:before {
	content: "\f16d"
}

.fa-youtube:before {
	content: "\f167"
}

.fa-chevron-left:before {
	content: "\f053"
}

.fa-chevron-right:before {
	content: "\f054"
}

.fa-chevron-up:before {
	content: "\f077"
}

.fa-bars:before,
.fa-navicon:before,
.fa-reorder:before {
	content: "\f0c9"
}

.fa-plus:before {
	content: "\f067"
}

.fa-star:before {
	content: "\f005"
}

.fa-quote-left:before {
	content: "\f10d"
}

.fa-quote-right:before {
	content: "\f10e"
}

.fa-arrow-right:before {
	content: "\f061"
}

.fa-pinterest:before {
	content: "\f0d2";
	font-size: 20px
}

.fa-eye:before {
	content: "\f06e"
}

.fa-user:before {
	content: "\f007"
}

.fa-calendar:before {
	content: "\f073"
}

.fa-paper-plane-o:before {
	content: "\f1d9"
}

.fa-newspaper-o:before {
	content: "\f1ea"
}

.fa-clock-o:before {
	content: "\f017"
}

.counter-top-area,
.counter-top-area1 {
	background: url(../../images/latest-form-loc.jpg) center center/cover no-repeat fixed
}

.support-section .form-inner form#quick-form:after {
	background: #2A2B51;
	-webkit-transform: skewY(-174deg);
	-ms-transform: skewY(-174deg);
	transform: skewY(-174deg);
	content: "";
	height: 70px;
	left: 0;
	position: absolute;
	top: -35px;
	width: 100%
}

.counter-top-area .form-overlay,
.counter-top-area1 .form-overlay {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%
}

.support-section .form-inner {
	padding-right: 40px;
	margin-top: 2%
}

.support-section .form-inner form#quick-form {
	margin-top: -3px;
	padding: 23px 55px;
	background: #2A2B51;
	position: relative;
	z-index: 1
}

.support-section .need-query h3 {
	margin: 0 0 30px 28px;
	font-size: 20px;
	text-align: center
}


.support-section h3 {
	margin-bottom: 20px;
	color: #fff;
	font-size: 30px;
	line-height: 31px;
	letter-spacing: 1px;
	font-weight: 700
}

.support-section .form-inner form#quick-form .need-query,
.support-section .form-inner form#quick-form .quick-form {
	position: relative;
	z-index: 111
}

button,
input,
optgroup,
select,
textarea {
	margin: 0;
	font: inherit;
	color: inherit
}

.support-section button,
.support-section input,
.support-section select,
.support-section textarea {
	width: 100%;
	background: 0;
	color: #fff;
	border: 1px solid #fff;
	padding: 6px 10px 6px 20px;
	outline: unset;
	margin-bottom: 4%
}

button,
input,
select,
textarea {
	font-family: inherit;
	font-size: inherit;
	line-height: inherit
}

.counter-top-area select option,
.counter-top-area1 select option,
.support-section input.submit:hover {
	background: #1b375d;
	color: #fff
}

.support-section textarea {
	height: 70px;
	padding-top: 2% !important
}

.support-section input.submit {
	background: #ffffff;
	color: #1b375d;
	border: 1px solid #fff;
	cursor: pointer;
	font-weight: 700;
	-webkit-transition: .25s;
	-o-transition: .25s;
	transition: .25s;
	width: 232px;
	font-size: 16px;
	letter-spacing: 1px;
	height: 40px;
	line-height: 40px;
	text-align: center;
	margin: 0 auto;
	padding: 0;
	display: block;
	border-radius: 30px
}

.support-section input[type=checkbox] {
	width: 20px;
	float: left;
	position: relative
}

.support-section label {
	width: 90%;
	float: left;
	color: #fff;
	margin-top: -5px;
	margin-left: 5px;
	font-size: 14px
}

.counter-top-area .list,
.counter-top-area1 .list {
	padding-bottom: 37% !important
}

.counter-top-area .sidebar-count .rs-counter-list h3,
.counter-top-area1 .sidebar-count .rs-counter-list h3 {
	margin-top: 9px !important
}

.counter-top-area .rs-counter-list h3,
.counter-top-area1 .rs-counter-list h3 {
	font-size: 30px;
	font-weight: 700;
	padding: 0;
	color: #000;
	margin-top: 20px !important;
	margin-bottom: 22px
}

.counter-top-area .rs-counter-list h4,
.counter-top-area1 .rs-counter-list h4 {
	color: #000;
	font-weight: 500;
	letter-spacing: 0;
	margin-bottom: 0;
	margin-left: 5px;
	font-size: 17px
}

.counter-top-area .sidebar-count .rs-counter-list,
.counter-top-area1 .sidebar-count .rs-counter-list {
	border: 0;
	padding-top: 0;
	padding-bottom: 35px
}

.counter-top-area .rs-counter-list,
.counter-top-area1 .rs-counter-list {
	text-align: center;
	padding: 25px 20px;
	border: 3px solid rgba(255, 255, 255, .5);
	-webkit-transition: .2s;
	-o-transition: .2s;
	transition: .2s
}

.support-section button,
.support-section input,
.support-section select,
.support-section textarea>click {
	border: 1px solid #fff
}

.rs-vertical-middle {
	display: -ms-flexbox;
	display: -webkit-box;
	display: flex;
	-ms-flex-wrap: wrap;
	flex-wrap: wrap;
	-ms-flex-align: center;
	-webkit-box-align: center;
	align-items: center
}

.change-side {
	margin-bottom: -10%
}

.blog,
.now-use {
	background-color: #fff;
	padding-bottom: 1%
}

.blog-details .book7-head h3,
.logo,
.mediaimg,
.overlay {
	text-align: center
}

.overlay--dark {
	background: #fff
}

.quick-form input::-webkit-input-placeholder,
.quick-form textarea::-webkit-input-placeholder {
	color: #fff;
	opacity: 1
}

.add-button p {
	word-spacing: 2px
}

.latest__item:hover .latest__item--inner,
.latest__item:hover .overlay {
	opacity: 1
}

a.au-btn--big.au-btn--pill:hover {
	background-color: #1b375d;
	color: #fff;
	text-decoration: none
}

.authorised-partners .padding-side {
	padding: 2%;
	list-style: none
}

.authorised-partners ul li {
	/*width: 25%*/
	width: 100%
}

.authorised-partners ul .set-size-1 {
	width: 12.5%
}

.padding-side img {
	width: 80%;
	margin: 20px;
	display: block
}

.contact,
.date--blog3 {
	background: #ebcd1e
}

.fournotfour-content {
	padding: 30px 20px 70px
}

.notfoundimage {
	background-repeat: no-repeat;
	background-position: center;
	background-size: cover;
	padding-top: 100px
}

.notfound {
	margin-left: 50px
}

.notfound .notfoundtext {
	padding-top: 250px;
	padding-bottom: 40px;
	text-align: center;
	color: #000
}

.notfound a.notfoundlink {
    color: #ffffff;
	background: #2A2B51;
    padding: 12px 30px;
    font-size: 17px;
    font-weight: 600;
    border-radius: 50px;
}

.notfound a:hover {
	background-color: #595875;
    color: #fff;
    text-decoration: none;
}


.advantage1,
.advantage2,
.advantage3,
.advantage4,
.advantage5,
.advantage6,
.advantage7,
.advantage8 {
	background-image: url(/images/advantage6.png);
	height: 65px;
	background-repeat: no-repeat
}

.advantage1 {
	background-position: 45% -3%
}

.advantage2 {
	background-position: 45% 12%
}

.advantage3 {
	background-position: 45% 27%
}

.advantage4 {
	background-position: 45% 42%
}

.advantage5 {
	background-position: 45% 57%
}

.advantage6 {
	background-position: 45% 72%
}

.advantage7 {
	background-position: 45% 88%
}

.advantage8 {
	background-position: 45% 103%
}

.foot-image,
.foot-image1 {
	background-image: url(../../images/footer-call1.png);
	height: 35px;
	background-repeat: no-repeat;
	background-size: 11%
}

.pt-10 {
	padding-top: 10px
}

h2 {
	color: #333;
	text-align: center;
	font-family: Roboto, sans-serif;
	font-weight: 700;
	position: relative;
	margin: 30px 0 60px
}

.carousel .carousel-item .img-box {
	width: 135px;
	height: 135px;
	margin: 0 auto;
	padding: 5px
}

.carousel .img-box img {
	width: 100%;
	height: 100%;
	display: block;
	border-radius: 50%
}

.carousel .overview {
	font-style: italic
}

.carousel .overview b {
	color: #7aa641
}

.carousel-control-next i,
.carousel-control-prev i {
	font-size: 68px;
	line-height: 42px;
	position: absolute;
	display: inline-block;
	color: rgba(0, 0, 0, .8);
	text-shadow: 0 3px 3px #e6e6e6, 0 0 0 #000
}

.carousel-indicators li,
.carousel-indicators li.active {
	width: 12px;
	height: 12px;
	margin: 1px 3px;
	border-radius: 50%;
	border: 0
}

.carousel-indicators li {
	background: #999;
	border-color: transparent;
	-webkit-box-shadow: inset 0 2px 1px rgba(0, 0, 0, .2);
	box-shadow: inset 0 2px 1px rgba(0, 0, 0, .2);
	cursor: pointer
}

.carousel-indicators li.active {
	background: #555;
	-webkit-box-shadow: inset 0 2px 1px rgba(0, 0, 0, .2);
	box-shadow: inset 0 2px 1px rgba(0, 0, 0, .2)
}


/* Global rule for all carousel items */
.carousel-item {
    height: 500px !important; /* Set a consistent height for all carousel items */
    background-position: center !important;
    background-size: cover !important;
    background-repeat: no-repeat !important;
}

.carousel-item.item8 {
	background-image: url(/images/slider/photo-1618221195710-dd6b41faaea6.jpeg);
}

.carousel-item.item9 {
	background-image: url(/images/bannerImg/brick_laying_top_banner.jpg);
}

.carousel-item.item2 {
	background-image: url(/images/slider/interior_designers_slider_1.jpg);
}

.carousel-item.item3 {
	background-image: url(/images/bannerImg/construction-industry-site.jpg);
}

.w3l-space-banner {
	padding-top: 9em
}

.carousel-caption {
	background: rgba(16, 16, 16, .2);
	text-align: center;
	position: inherit;
	font-family: 'Silka Bold';
	bottom: 25% !important;
	background: -webkit-gradient(linear, left top, right top, color-stop(0, rgba(0, 0, 0, 0)), color-stop(50%, rgba(16, 16, 16, .4)), to(rgba(0, 0, 0, 0)));
	background: -o-linear-gradient(left, rgba(0, 0, 0, 0) 0, rgba(16, 16, 16, .4) 50%, rgba(0, 0, 0, 0) 100%);
	background: linear-gradient(to right, rgba(0, 0, 0, 0) 0, rgba(16, 16, 16, .4) 50%, rgba(0, 0, 0, 0) 100%)
}

.carousel-caption h1 {
	color: #fff;
	font-size: 32px;
	text-transform: capitalize;
	text-shadow: #000 2px 1px;
	margin-bottom: 20px;
	font-family: 'Silka Bold';
}

.carousel-caption h2 {
	color: #fff;
	font-size: 32px;
	text-transform: capitalize;
	text-shadow: #000 2px 1px;
	margin-bottom: 20px;
	font-family: 'Silka Bold';
}

.carousel-caption h1 {
    color: #fff;
    font-size: 32px;
    text-transform: capitalize;
    text-shadow: #000 2px 1px;
    margin-bottom: 20px;
    font-family: 'Silka Regular', sans-serif !important; /* Use 'Silka Regular' */
}

.carousel-caption h2 {
    color: #fff;
    font-size: 32px;
    text-transform: capitalize;
    text-shadow: #000 2px 1px;
    margin-bottom: 20px;
    font-family: 'Silka Regular', sans-serif !important; /* Use 'Silka Regular' */
}
.carousel-caption A {
    font-family: 'Silka Regular', sans-serif !important; /* Use 'Silka Regular' */
}

.carousel-caption h6 {
    color: #fff;
    font-size: 18px;
    font-family: 'Silka Regular'
}
.box-container .paginate ul,
.carousel-caption ul {
	margin: auto
}

.carousel-caption p {
	letter-spacing: 3px;
	font-size: 34px;
	color: #fff;
	text-shadow: #000 2px 1px;
	margin-bottom: 0
}

.carousel-indicators {
	margin-top: 20px;
	bottom: 4%
}

.btn-one {
	background-color: #f47e45;
	padding: 10px 40px !important;
	border-radius: 20px;
	font-size: 17px !important;
	cursor: pointer;
	text-transform: capitalize;
	-webkit-transition: .5s;
	-o-transition: .5s;
	transition: .5s;
	font-weight: 600 !important;
	text-shadow: none;
	margin-top: 3rem
}
.btn-cta {
	background-color: #f47e45;
	padding: 10px 40px;
	border-radius: 20px;
	font-size: 17px !important;
	cursor: pointer;
	text-transform: capitalize;
	-webkit-transition: .5s;
	-o-transition: .5s;
	transition: .5s;
	font-weight: 600 !important;
	text-shadow: none;
	margin-top: 3rem
}

.btn-one:hover {
	background-color: #fff;
	color: #000 !important;
	text-decoration: none
}

@media(max-width:1366px) {

	.carousel-control-next,
	.carousel-control-prev {
		width: 4%
	}

	.carousel-caption {
		margin-top: 5%
	}

	.carousel-item.item4,
	.carousel-item.item8 {
		background-position: 0 100%
	}
}

@media(max-width:1080px) {
	.carousel-caption {
		margin-top: 5%
	}

	.carousel-caption p {
		letter-spacing: 3px;
		font-size: 35px
	}

	.carousel-caption h1,
	.carousel-caption h2 {
		font-size: 30px
	}

	.w3l-space-banner {
		padding-top: 5em
	}

	/* All carousel items maintain the same height */
	.carousel-item {
		height: 500px !important
	}

	.carousel-item.item4,
	.carousel-item.item8 {
		background-position: 0 100%
	}
}

@media(max-width:991px) {
	.carousel-caption {
		margin-top: 5%
	}

	.carousel-caption p {
		letter-spacing: 3px;
		font-size: 30px
	}

	.carousel-caption h1,
	.carousel-caption h2 {
		font-size: 30px
	}

	.button2 {
		padding: 11px 20px;
		font-size: 14px
	}

	/* All carousel items maintain the same height */
	.carousel-item {
		height: 430px !important
	}

	.carousel-item.item4,
	.carousel-item.item8 {
		background-position: 0 100%
	}
}

@media(max-width:768px) {
	.notfoundimage {
		background-repeat: no-repeat;
		background-position: center;
		background-size: cover;
		padding-top: 20px;
		margin: auto
	}

	.notfound,
	.notfound a.notfoundlink {
		margin: auto
	}

	.notfound .notfoundtext {
		padding-top: 50px
	}

	.foot-image {
		background-position: 5% 115%
	}

	.foot-image1 {
		background-position: 5% -5%
	}

	.carousel-caption h1,
	.carousel-caption h2 {
		font-size: 25px
	}

	/* All carousel items maintain the same height */
	.carousel-item {
		height: 400px !important
	}

	.carousel-item.item4,
	.carousel-item.item8 {
		background-position: 0 100%
	}
}

@media(max-width:736px) {

	.carousel-item.item1,
	.carousel-item.item2,
	.carousel-item.item3,
	.carousel-item.item4,
	.carousel-item.item5,
	.carousel-item.item6,
	.carousel-item.item8 {
		min-height: 380px
	}

	.w3l-space-banner {
		padding-top: 4em
	}

	.carousel-caption p {
		font-size: 22px
	}

	.carousel-item.item4,
	.carousel-item.item8 {
		background-position: 0 100%
	}

	.blog-details .blog-title {
		padding: 20% 0 2%
	}

	.pos-resize {
		text-align: center;
		position: absolute;
		top: 56%;
		margin: 4.8rem auto 0;
		width: 100%
	}
}

@media(max-width:568px) {

	.carousel-control-next,
	.carousel-control-prev {
		width: 2%
	}

	.foot-image {
		background-position: 5% 110%
	}

	.foot-image1 {
		background-position: 5% 0
	}

	.btn-one {
		display: none
	}

	.choose-2 {
		height: 25rem
	}

	.blog-details .blog-title {
		padding: 20% 0 2%
	}

	.pos-resize {
		text-align: center;
		position: absolute;
		top: 56%;
		margin: 4.8rem auto 0;
		width: 100%
	}

.carousel-item.item1,
.carousel-item.item5 {
    background: url(../../images/bannerImg/construction-industry-site.jpg) center/cover no-repeat;
    -webkit-background-size: cover;
    -moz-background-size: cover;
    -o-background-size: cover;
    -ms-background-size: cover;
    background-size: cover;
    height: 90vh;
}

.carousel-item.item3 {
    background: url(../../images/bannerImg/construction-industry-site.jpg) center/cover no-repeat;
    -webkit-background-size: cover;
    -moz-background-size: cover;
    -o-background-size: cover;
    -ms-background-size: cover;
    background-size: cover;
    height: 90vh;
}

.carousel-item.item6 {
    background: url(../../images/bannerImg/construction-industry-site.jpg) center/cover no-repeat;
    -webkit-background-size: cover;
    -moz-background-size: cover;
    -o-background-size: cover;
    -ms-background-size: cover;
    background-size: cover;
    height: 90vh;
}

	.carousel-item.item4,
	.carousel-item.item8 {
		background: url(/images/slider/photo-1618221195710-dd6b41faaea6.jpeg) center/cover no-repeat;
		-webkit-background-size: cover;
		-moz-background-size: cover;
		-o-background-size: cover;
		-ms-background-size: cover;
		height: 90vh
	}
	.carousel-item.item4,
		.carousel-item.item2 {
		background: url(/images/slider/interior_designers_slider_1.jpg) center/cover no-repeat;
		-webkit-background-size: cover;
		-moz-background-size: cover;
		-o-background-size: cover;
		-ms-background-size: cover;
		height: 90vh
	}
}

@media(max-width:480px) {

	.carousel-caption h1,
	.carousel-caption h2 {
		font-size: 23px
	}

	.foot-image {
		background-position: 5% 130%
	}

	.foot-image1 {
		background-position: 5% -10%
	}

	.btn-one {
		display: none
	}

	.blog-details .blog-title {
		padding: 20% 0 2%
	}

	.choose-2 {
		height: 25rem
	}

	.pos-resize {
		text-align: center;
		position: absolute;
		top: 56%;
		margin: 4.8rem auto 0;
		width: 100%
	}

	.carousel-item.item1,
	.carousel-item.item5 {
		background: url(../../images/bannerImg/home-interior-designers-mobile-view.jpg) center/cover no-repeat;
		-webkit-background-size: cover;
		-moz-background-size: cover;
		-o-background-size: cover;
		-ms-background-size: cover;
		height: 90vh
	}

	.carousel-item.item3 {
		background: url(../../images/bannerImg/construction-industry-site.jpg) center/cover no-repeat;
		-webkit-background-size: cover;
		-moz-background-size: cover;
		-o-background-size: cover;
		-ms-background-size: cover;
		background-size: cover;
		height: 90vh
	}

	.carousel-item.item6 {
		background: url(../../images/bannerImg/construction-industry-site.jpg) center/cover no-repeat;
		-webkit-background-size: cover;
		-moz-background-size: cover;
		-o-background-size: cover;
		-ms-background-size: cover;
		background-size: cover;
		height: 90vh
	}

	.carousel-item.item4 {
		background: url(../../images/bannerImg/construction-industry-site.jpg) center/cover no-repeat;
		-webkit-background-size: cover;
		-moz-background-size: cover;
		-o-background-size: cover;
		-ms-background-size: cover;
		height: 90vh
	}

	.carousel-item.item2 {
		background: url(/images/slider/interior_designers_slider_1.jpg) center/cover no-repeat;
		-webkit-background-size: cover;
		-moz-background-size: cover;
		-o-background-size: cover;
		-ms-background-size: cover;
		height: 90vh
	}


	.carousel-item.item8 {
		background: url(/images/slider/photo-1618221195710-dd6b41faaea6.jpeg) center/cover no-repeat;
		-webkit-background-size: cover;
		-moz-background-size: cover;
		-o-background-size: cover;
		-ms-background-size: cover;
		height: 90vh
	}
}

@media(max-width:440px) {

	.carousel-item.item1,
	.carousel-item.item2,
	.carousel-item.item3,
	.carousel-item.item4,
	.carousel-item.item5,
	.carousel-item.item6 {
		min-height: 300px
	}

	.carousel-caption h1,
	.carousel-caption h2 {
		font-size: 18px
	}

	.w3l-space-banner {
		padding-top: 2em
	}

	.foot-image {
		background-position: 5% 140%
	}

	.foot-image1 {
		background-position: 5% -20%
	}

	.blog-details .blog-title {
		padding: 20% 0 2%
	}

	.btn-one {
		display: flex !important;
	}

	.choose-2 {
		height: 25rem
	}

	.pos-resize {
		text-align: center;
		position: absolute;
		top: 56%;
		margin: 4.8rem auto 0;
		width: 100%
	}

	.carousel-item.item1,
	.carousel-item.item5 {
		background: url(../../images/bannerImg/home-interior-designers-mobile-view.jpg) center/cover no-repeat;
		-webkit-background-size: cover;
		-moz-background-size: cover;
		-o-background-size: cover;
		-ms-background-size: cover;
		height: 90vh
	}

	.carousel-item.item3,
	.carousel-item.item6 {
		background: url(../../images/bannerImg/home-interior-designers-mobile-view.jpg) center/cover no-repeat;
		-webkit-background-size: cover;
		-moz-background-size: cover;
		-o-background-size: cover;
		-ms-background-size: cover;
		height: 90vh
	}

	.carousel-item.item2 {
		background: url(/images/slider/interior_designers_slider_1.jpg) center/cover no-repeat;
		-webkit-background-size: cover;
		-moz-background-size: cover;
		-o-background-size: cover;
		-ms-background-size: cover;
		height: 90vh
	}
	.carousel-item.item4 {
		background: url(../../images/bannerImg/home-interior-designers-mobile-view.jpg) center/cover no-repeat;
		-webkit-background-size: cover;
		-moz-background-size: cover;
		-o-background-size: cover;
		-ms-background-size: cover;
		height: 90vh
	}

	.carousel-item.item8 {
		background: url(/images/slider/photo-1618221195710-dd6b41faaea6.jpeg) center/cover no-repeat;
		-webkit-background-size: cover;
		-moz-background-size: cover;
		-o-background-size: cover;
		-ms-background-size: cover;
		height: 90vh
	}
}

@media(max-width:414px) {

	.carousel-caption h1,
	.carousel-caption h2 {
		font-size: 22px
	}

	.foot-image {
		background-position: 5% 140%
	}

	.foot-image1 {
		background-position: 5% -20%
	}

	.blog-details,
	.blog-details .blog-title {
		padding: 20% 0 2%
	}

	.choose-2 {
		height: 25rem
	}

	.pos-resize {
		text-align: center;
		position: absolute;
		top: 56%;
		margin: 4.8rem auto 0;
		width: 100%
	}

	.carousel-item.item1,
	.carousel-item.item5 {
		background: url(../../images/bannerImg/interior_designers_slider_1.jpg) center/cover no-repeat;
		-webkit-background-size: cover;
		-moz-background-size: cover;
		-o-background-size: cover;
		-ms-background-size: cover;
		height: 90vh
	}

	.carousel-item.item3 {
		background: url(../../images/bannerImg/construction-industry-site.jpg) center/cover no-repeat;
		-webkit-background-size: cover;
		-moz-background-size: cover;
		-o-background-size: cover;
		-ms-background-size: cover;
		background-size: cover;
		height: 90vh
	}

	.carousel-item.item2 {
		background: url(/images/slider/interior_designers_slider_1.jpg) center/cover no-repeat;
		-webkit-background-size: cover;
		-moz-background-size: cover;
		-o-background-size: cover;
		-ms-background-size: cover;
		height: 90vh
	}
	.carousel-item.item4 {
		background: url(../../images/bannerImg/interior_designers_slider_1.jpg) center/cover no-repeat;
		-webkit-background-size: cover;
		-moz-background-size: cover;
		-o-background-size: cover;
		-ms-background-size: cover;
		height: 90vh
	}

	.carousel-item.item6 {
		background: url(../../images/bannerImg/interior_designers_slider_1.jpg) center/cover no-repeat;
		-webkit-background-size: cover;
		-moz-background-size: cover;
		-o-background-size: cover;
		-ms-background-size: cover;
		height: 0
	}

	.carousel-item.item8 {
		background: url(/images/slider/photo-1618221195710-dd6b41faaea6.jpeg) center/cover no-repeat;
		-webkit-background-size: cover;
		-moz-background-size: cover;
		-o-background-size: cover;
		-ms-background-size: cover;
		height: 90vh
	}
}

@media(max-width:384px) {

	.carousel-caption h1,
	.carousel-caption h2 {
		font-size: 20px
	}

	.carousel-caption p {
		font-size: 22px
	}

	.button2 {
		padding: 10px 16px;
		font-size: 14px
	}

	.carousel-item.item1,
	.carousel-item.item2,
	.carousel-item.item3,
	.carousel-item.item4,
	.carousel-item.item5,
	.carousel-item.item6 {
		min-height: 270px
	}

	h3.heading-tittle {
		font-size: 26px
	}

	.carousel-item.item4 {
		background-position: 0 100%
	}

	.blog-details,
	.blog-details .blog-title {
		padding: 20% 0 2%
	}

	.foot-image {
		background-position: 5% 140%
	}

	.foot-image1 {
		background-position: 5% -20%
	}

	.pos-resize {
		text-align: center;
		position: absolute;
		top: 56%;
		margin: 4.8rem auto 0;
		width: 100%
	}
}

@media(max-width:375px) {

	.carousel-caption h1,
	.carousel-caption h2 {
		font-size: 17px
	}

	.carousel-item.item4 {
		background-position: 0 100%
	}

	.foot-image {
		background-position: 3% 170%
	}

	.foot-image1 {
		background-position: 5% -20%
	}

	.blog-details .blog-title {
		padding: 20% 0 2%
	}

	#left {
		height: 90px
	}
}

@media(max-width:320px) {
	.carousel-caption p {
		font-size: 22px;
		letter-spacing: 2px
	}

	.carousel-caption h1,
	.carousel-caption h2 {
		font-size: 19px
	}

	.button2 {
		font-size: 14px
	}

	.carousel-item.item1,
	.carousel-item.item2,
	.carousel-item.item3,
	.carousel-item.item4,
	.carousel-item.item5,
	.carousel-item.item6 {
		min-height: 240px
	}

	.carousel-item.item4 {
		background-position: 0 100%
	}

	.foot-image {
		background-position: 2% 180%
	}

	.foot-image1 {
		background-position: 5% -50%
	}

	#left {
		height: 90px
	}

	.blog-details .blog-title {
		padding: 20% 0 2%
	}
}

.ourprocess .col-md-6,
nav.navbar {
	padding: 0
}

nav.navbar.bootsnav {
	margin-bottom: 0;
	border-radius: 0;
	background-color: #fff;
	z-index: 11
}

nav.navbar.bootsnav.navbar-fixed {
	position: fixed;
	display: block;
	width: 100%
}

.wrap-sticky {
	position: relative;
	-webkit-transition: .3s ease-in-out;
	-o-transition: .3s ease-in-out;
	transition: .3s ease-in-out
}

.wrap-sticky nav.navbar.bootsnav {
	position: absolute;
	width: 100%;
	left: 0;
	top: 0
}

.wrap-sticky nav.navbar.bootsnav.sticked {
	position: fixed;
	-webkit-transition: .2s ease-in-out;
	-o-transition: .2s ease-in-out;
	transition: .2s ease-in-out
}

body.on-side .wrap-sticky nav.navbar.bootsnav.sticked {
	left: -280px
}

.pos-resize {
	text-align: center;
	position: absolute;
	top: 56%;
	margin: 1rem auto 0;
	width: 100%
}

.pos-resize h1,
.pos-resize h2 {
	font-size: 32px;
	color: #fff;
	margin: 0 auto;
	padding: 1% 2%;
	width: 100%;
	background: #21201fc2;
	-webkit-box-shadow: 0 0 #000;
	box-shadow: 0 0 #000;
	text-transform: capitalize;
	font-weight: 700;
	letter-spacing: 2px
}
.pos-resize p{
    color:white;
}
.breadlist {
	width: auto;
	margin: 5px;
	position: absolute;
	top: 90%;
	left: 13%
}

.breadlist ul li a:hover {
	color: grey
}

.breadlist a {
	position: relative; /* Ensures it's above other elements */
	z-index: 9999;
	pointer-events: auto; /* Ensures clicks work */
	color: black; /* Make sure it's visible */
	text-decoration: none;
}

.add-text {
	margin: 2rem 0 2%
}

.add-button p,
.add-text p {
	color: #000;
	font-size: 14px;
	word-spacing: 2px;
	margin: 5px 0
}

.rep-left,
.rep-left img {
	margin-bottom: 10%
}

.add-text ol {
	padding-left: 4%
}

.box-container .gray-shedow {
	-webkit-box-shadow: 2px 3px 5px 0 #d0d2d5;
	box-shadow: 2px 3px 5px 0 #d0d2d5;
	border-radius: 24px;
	-webkit-transition: -webkit-transform 1s;
	transition: -webkit-transform 1s;
	-o-transition: transform 1s;
	transition: transform 1s;
	transition: transform 1s, -webkit-transform 1s
}

.box-container h3,
.box-container h4 {
	font-size: 17px;
	padding-bottom: 4%;
	letter-spacing: 0;
	font-weight: 700
}

.add-button h1,
.add-button h2,
.add-button h3 {
	font-size: 19px;
	word-spacing: 5px
}

.box-container img {
	border-radius: 24px
}

.rep-left {
	padding: 2% 10%
}

.add-button .interior-1 {
	background: #f47e45;
	color: #fff;
	padding: 10px 63px;
	font-size: 16px
}

.add-button .interior-1:hover {
	background: #1b375d;
	border: 1px solid;
	text-decoration: none
}

.add-button {
	margin: 0 0 3%
}

.box-container h2 {
	padding-bottom: 3%
}

.shade-now {
	padding: 1% 2% 2%
}

.add-button h1 {
	word-spacing: 5px
}

.servicebtn {
	padding: 5px 20px;
	font-size: 16px;
	border-radius: 10px
}

.box-container .gray-shedow:hover {
	background-color: #fff;
	outline: #fff solid 1px;
	border-radius: 4px;
	-webkit-box-shadow: 0 0 2px 2px #d0d2d5;
	box-shadow: 0 0 2px 2px #d0d2d5;
	-webkit-transform: scale(1.1);
	-ms-transform: scale(1.1);
	transform: scale(1.1)
}

.add-button h1,
.add-button h2 {
	margin-bottom: 1%;
	margin-top: 2%
}

.ourprocess {
	padding: 30px 0 70px
}

.ourprocess img {
	-webkit-transition: -webkit-transform 1s;
	transition: -webkit-transform 1s;
	-o-transition: transform 1s;
	transition: transform 1s;
	transition: transform 1s, -webkit-transform 1s;
	border: 1px solid #333f552e
}

.ourprocess img:hover {
	-webkit-transform: scale(1.022);
	-ms-transform: scale(1.022);
	transform: scale(1.022)
}

.ourprocess .add-bg-1 {
	background: #fafbd7
}

.ourprocess .fix-box {
	padding-left: 70px;
	padding-right: 70px;
	padding-top: 0
}

.ourprocess h2 {
	margin-bottom: 5px;
	font-size: 24px
}

.ourprocess h2>span {
	font-size: 60px;
	font-weight: 700;
	color: #d60000
}

.ourprocess .fix-box p {
	font-size: 14px;
	padding-bottom: 2%
}

.ourprocess .add-bg-2 {
	background: #f6fdff
}

.project1 {
	padding: 60px 0 20px
}

#filter-wrap {
	margin-bottom: 40px;
	text-align: center
}

#filter li {
	margin: 0 30px
}

#filter li span {
	font-size: 18px;
	color: #333;
	cursor: pointer;
	-webkit-transition: .6s;
	-o-transition: .6s;
	transition: .6s
}

.dual-container {
	padding: 3% 0 2%
}

.dual-container h3 {
	padding-bottom: 15px
}

.project-container-1:hover h4 {
	display: block;
	text-align: center;
	color: #fff;
	font-weight: 700
}

.project-container-1 h4>a:hover {
	color: #ebcd1e;
	text-decoration: none;
	text-shadow: 1px 1px #000
}

.dual-container-1 h3,
.project-container h3 {
	font-size: 18px;
	padding-bottom: 0;
	word-spacing: 5px;
	letter-spacing: 1px;
	color: #000;
	text-align: left
}

.dual-container-1 h3 span,
.project-container h3 span {
	color: #ebcd1e;
	font-size: 18px;
	font-weight: 700
}

.mediaroom-img,
.project-container-1,
.project-otherimg {
	padding: 5px
}

.project-container img {
	width: 100%;
	height: 230px
}

.project-container .box-1 {
	position: absolute;
	top: 100px;
	width: 90%;
	color: #fff;
	text-align: center
}

.project-container-1 h4 {
	color: #fff;
	display: none
}

.project-container img:hover {
	opacity: .6
}

.blogsection {
	margin: 3% 0 1%;
	text-align: center
}

.blogsection h2 {
	margin-bottom: 30px
}

.box-container .border {
	border-bottom: 2px solid #ebcd1e !important;
	width: 80%;
	margin-bottom: 20px;
	margin-left: 0;
	margin-top: 0
}

.box-container ul li {
	padding-bottom: 4%
}

.box-container p {
	font-size: 16px;
	color: #757575;
	padding: 3%
}

.box-container .gray-shedow-blog {
	-webkit-box-shadow: 2px 3px 5px 0 #d0d2d5;
	box-shadow: 2px 3px 5px 0 #d0d2d5;
	border-radius: 24px
}

.box-container .gray-shedow-blog .rep-left-blog {
	padding: 2% 5%;
	margin-bottom: 10%
}

.rep-left-blog1 {
	margin-bottom: 10%;
	border: 1px solid #eee
}

.rep-left-blog img {
	margin-bottom: 5%;
	border-radius: 1px;
	width: 390px;
	height: 260px
}

.box-container h4 {
	font-size: 17px;
	padding: 3% 3% 0;
	letter-spacing: 0
}

.rep-left-blog ul {
	padding: 0 10px
}

.rep-left-blog ul li {
	margin-right: 10px !important;
	padding-bottom: 1%
}

.rep-left-blog ul li .fa {
	margin-right: 5px;
	color: #d60000
}

.page-item.active .page-link {
	background-color: #d60000 !important;
	border-color: #d60000 !important
}

.blog-details {
	padding: 2% 0
}

.blog-details .blog-title {
    text-transform: capitalize !important;
	margin-bottom: 3px;
	margin-top: 2% !important;
	font-size: 25px;
	color: #333
}

.blog-details p {
	font-size: 16px;
	color: #999
}

.blog-details h3 {
	font-size: 18px;
	padding-bottom: 0;
	margin-top: 3%;
	word-spacing: 1px;
	letter-spacing: 1px;
	margin-bottom: 3%;
	color: #000;
	text-align: left
}

.blog-details .border {
	border-bottom: 2px solid #d60000 !important;
	width: 86px;
	margin-bottom: 20px;
	margin-left: 0;
	margin-top: 0
}

.blog-details .down {
	margin-bottom: 5%
}

.blog-details ul {
	list-style-type: disc;
	font-size: 16px;
	padding-left: 20px
}

.blog-details .social-share ul li i {
	font-size: 30px;
	color: #ebcd1e;
	padding-top: 1%
}

.blog-details ul li i {
	font-size: 16px;
	color: #d60000;
	padding-top: 1%
}

.blog-details .book-now-form7 {
	margin: 0 0 7%;
	background-color: #fff;
	-webkit-box-shadow: 0 0 1px 1px rgba(0, 0, 0, .3);
	box-shadow: 0 0 1px 1px rgba(0, 0, 0, .3)
}

.blog-details .book7-head {
	background-color: #ebcd1e;
	position: relative;
	padding: 3px 15px
}

.blog-details .book-form7 {
	padding: 7px 5px;
	width: 100%;
	margin-top: 2%;
	color: #000
}

input,
textarea {
	-o-transition: border-color .5s;
	transition: border-color .5s;
	-moz-transition: border-color .5s;
	-webkit-transition: border-color .5s
}

.blog-details .book-form7 .form1-btn,
.book-form7 .form1-div {
	margin: 0 auto 10px;
	text-align: center;
	padding: 0 10px
}

.blog-details .book-form7 .form1-input,
.blog-details .book-form7 select.form1-input {
	color: #000;
	border: 1px solid #ebcd1e;
	background-color: transparent;
	border-radius: 0;
	font-size: 14px;
	height: 33px;
	padding: 0 4%;
	position: relative;
	width: 100%
}

.blog-details .book-form7 .form1-textarea {
	color: #000;
	font-size: 14px;
	border: 1px solid #ebcd1e;
	background-color: transparent;
	border-radius: 0;
	position: relative;
	width: 100%;
	padding: 0 4%
}

.blog-details .book-form7 .btn {
	font-size: 17px;
	border-radius: 5px;
	padding: 6px 35px;
	cursor: pointer
}

.blog-details .btn.btn-1 {
	background-color: #ebcd1e;
	color: #fff;
	padding: 7px 15px
}

.blog-details .social-share li {
	display: inline;
	padding: 10px
}

.blog-details .blog-head ul.blog-meta li {
	padding-bottom: 1px
}

.blog-details .blog-head ul.blog-meta li .fa,
.blog-details ul.blog-most-read li .fa {
	margin-right: 10px
}

.blog-details .blog-most-read-title h6 .fa {
	font-size: 14px;
	margin-right: 10px
}

.blog-details .mostreadblog {
	display: block;
	position: relative;
	background-color: rgba(0, 0, 0, .5) !important
}

.blog-details .mostreadblog img {
	width: 100%
}

.blog-details .mostreadblog .mostreadtitle {
	position: absolute;
	z-index: 999;
	top: 0;
	text-align: center;
	padding: 77px 20px;
	background-color: rgba(0, 0, 0, .2);
	color: #000;
	font-weight: 700
}

.mostreadtitle ul li {
	margin-right: 20px !important
}

.blog-details .mostreadblog .mostreadtitle a>h4 {
	font-size: 18px;
	letter-spacing: 2px;
	font-weight: 700
}

.mediaroom-content a {
	background-color: #d60000;
	color: #fff;
	padding: 10px;
	border-radius: 10px
}

.contact-content {
	padding: 30px 0
}

.contact-content h3 {
	font-size: 23px;
	letter-spacing: .66px;
	margin-bottom: 10px;
	margin-top: 25px
}

.contact-content h4 {
	font-size: 20px;
	letter-spacing: .66px;
	margin-bottom: 3%
}

.contact-content input,
.contact-content select,
.contact-content textarea {
	width: 100%;
	outline: 0;
	background-color: #fff;
	font-size: 14px;
	padding: 10px;
	border: 1px solid #e0e6ea;
	max-width: 100%;
	margin: auto auto 2%;
	min-height: 42px
}

.contact-content form p {
	margin: .88% 0
}

.contact-content p {
	font-size: 16px;
	margin: 1% 0;

}

.contact-content input[type=submit] {
	color: #000;
	background: #fff;
	font-weight: 600;
	text-align: center;
	letter-spacing: 1px;
	font-size: 14px;
	border: 2px solid #ebcd1e;
	border-radius: 2px;
	width: 40%
}
.font-weight-bold {
    font-family :'Silka Bold';
}
.font-weight-regular{
    font-family: 'Silka Regular';
}
.we-are {
	padding: 40px 0 !important
}

.we-are__item {
	width: 200px;
	height: 200px;
	display: inline-block;
	margin-right: 20px
}

.we-are__right>h5 {
	font-weight: 600;
	text-transform: initial;
	margin-bottom: 19px;
	font-size: 16px;
	color: #333
}

.term-condition p,
.terms h3,
.terms h4,
.terms p {
	margin: 10px 0;
	float: left
}

.term-condition p,
.terms p {
	line-height: 25px
}

.term-condition h3,
.term-condition h4 {
	width: 100%;
	float: left;
	margin: 10px 0;
	font-weight: 600
}

.term-condition h4 {
	font-size: 21px
}

.terms h3 {
	width: 100%;
	font-size: 18px;
	font-weight: 600
}

.terms h4 {
	width: 100%;
	font-size: 21px;
	font-weight: 600
}

.terms strong {
	float: left;
	width: 100%
}

.terms ul {
	float: left;
	width: 100%;
	padding-left: 4%
}

.faq_section {
	margin: 40px auto
}

.FaQ_Each {
	padding-bottom: 10px
}

.box {
	color: #666;
	padding-top: 15px;
	padding-bottom: 15px;
	padding-left: 20px;
	font-size: 16px;
	text-transform: none;
	cursor: pointer;
	border: 1px solid #d9d9d9;

}

.draw {
	display: none;
	background: #fff;
	padding: 20px 20px 20px 30px;
	border-bottom: 1px solid #d9d9d9;
	border-left: 1px solid #d9d9d9;
	border-right: 1px solid #d9d9d9;
	color: #000
}

.draw ul li {
	margin-left: 20px
}

.draw a:hover {
	color: #000;
	text-decoration: none
}

.mediacontent ul li {
	list-style: none;
	color: #000;
	font-weight: 600
}

.mediaimg img {
	width: 600px;
	height: 29rem
}

.mediaimg span {
	display: block;
	font-weight: 600
}

@media(min-width:280px) and (max-width:767px) {
	.pos-resize {
		top: 60%
	}

	.add-button .col-xs-12 {
		text-align: center;
		margin-bottom: 9%
	}

	.breadlist {
		top: 85%;
		left: 10%
	}

	.pos-resize h1,
	.pos-resize h2 {
		font-size: 17px;
		width: 84%;
		margin-top: 0;
		padding: 2%
	}

	.ourprocess .fix-box {
		padding-left: 10px;
		padding-right: 10px;
		padding-top: 3%
	}

	.ourprocess h2 {
		margin-bottom: 5px;
		font-size: 20px
	}

	.ourprocess h2>span {
		font-size: 50px
	}

	.mediaroom-content {
		padding: 2rem
	}

	.blog-details h6 {
		margin-top: 2%
	}
}

@media(min-width:1360px) and (max-width:1380px) {
	.header-wrap {
		background: #fff;
		-webkit-box-shadow: 1px 0 3px #222;
		box-shadow: 1px 0 3px #222
	}

	.header-wrap .logo img {
		width: 10%;
		position: absolute
	}

	nav>ul>li {
		padding: 18px 7px
	}
}

@media(min-width:1024px) and (max-width:1100px) {
	.pos-resize {
		top: 62%
	}

	.pos-resize h1,
	.pos-resize h2 {
		font-size: 23px;
		width: 34%
	}

	.breadlist ul li,
	.breadlist ul li a {
		color: #fff;
		font-size: 14px
	}

	.add-button .interior-1 {
		padding: 10px 25px;
		font-size: 14px
	}
}

@media(min-width:768px) and (max-width:1023px) {
	.pos-resize {
		top: 40%
	}

	.breadlist {
		top: 85%;
		left: 10%
	}
}

@media(min-width:768px) {
	.choose-2 {
		display: none
	}

	.ourprocess .fix-box {
		padding-left: 10px;
		padding-right: 10px;
		padding-top: 3%
	}

	.ourprocess h2 {
		margin-bottom: 5px;
		font-size: 20px
	}

	.ourprocess h2>span {
		font-size: 35px
	}
}

.stickyForm,
.stickyForm.active {
	position: fixed;
	top: 20%;
	width: 310px;
	padding: 17px;
	z-index: 9999;
	text-align: center
}

@media(max-width:767px) {
	.choose-1 {
		display: none
	}
}

.stickyForm {
	right: 0;
	-webkit-transition: .35s ease-in-out;
	-o-transition: .35s ease-in-out;
	transition: .35s ease-in-out
}

.stickyForm.active {
	left: auto;
	bottom: auto;
	border-radius: 50px;
	right: -310px;
	-webkit-transition: .35s ease-in-out;
	-o-transition: .35s ease-in-out;
	transition: .35s ease-in-out
}

.stickyForm,
.stickyForm.active .btn {
	background: rgba(0, 0, 0, .8)
}

.stickyForm.active .btn {
	width: 65%;
	height: 40px;
	padding: 0;
	left: -120px;
	top: -160px;
	color: #f4c60e;
	background-color: #0a0a0a80;
	font-size: 20px;
	font-weight: 600
}

.stickyForm .btn {
	position: absolute;
	right: auto;
	font-size: 16px;
	border: 0;
	-webkit-perspective: 100px;
	perspective: 100px;
	outline: 0;
	-webkit-transform: rotate(-90deg);
	-moz-transform: rotate(-90deg);
	-o-transform: rotate(-90deg);
	-ms-transform: rotate(-90deg);
	top: -220px;
	bottom: -1px;
	margin: auto;
	left: -44px;
	color: #fff;
	height: 30px;
	width: 60px;
	cursor: pointer;
	border-radius: 10px 10px 0 0;
	border-left: 1px solid #0a0a0a80;
	border-right: 1px solid #0a0a0a80;
	border-top: 1px solid #0a0a0a80;
	padding: 1px;
	background: rgba(0, 0, 0, .9)
}

.stickyForm ::-webkit-input-placeholder {
	color: #fff
}

.stickyForm span {
	font-size: 14px;
	color: #414141
}

.stickyForm span a {
	color: #414141
}

.stickyForm span a:hover {
	text-decoration: underline
}

body .stickyForm [type=submit] {
	background: #0a0a0a80 !important;
	color: #fff;
	height: 40px !important;
	padding: 0 30px !important;
	border: 1px solid;
	cursor: pointer;
	border-radius: 15px;
	margin-top: 10px
}

body .stickyForm label {
	width: 100% !important
}

body .stickyForm .intl-tel-input,
body .stickyForm input[type=email],
body .stickyForm input[type=tel],
body .stickyForm input[type=text],
body .stickyForm select {
	width: 100% !important;
	border: 1px solid;
	color: #fff;
	height: 35px;
	background: 0 0;
	margin-bottom: 14px;
	border-radius: .25rem
}

body .stickyForm textarea {
	width: 100% !important;
	border: 1px solid;
	color: #fff;
	background: 0 0;
	margin-bottom: 14px;
	border-radius: .25rem
}

body .stickyForm label p {
	color: #fff;
	text-align: center
}

body .stickyForm .radio-inline {
	width: 100% !important;
	border: 0;
	color: #fff;
	height: 35px;
	background: 0 0;
	text-align: left;
	display: initial;
	margin-right: 5px
}

body .stickyForm .radio-inline>input[type=radio] {
	margin-right: 5px
}

@media only screen and (max-width:768px) {

	.stickyForm,
	.stickyForm.active {
		position: fixed;
		top: auto;
		left: auto;
		width: 100%;
		right: 0;
		text-align: center;
		padding: 17px;
		z-index: 9999999999;
		border-radius: 0
	}

	.stickyForm {
		bottom: -2px;
		background: rgba(0, 0, 0, .9);
		-webkit-transition: .35s ease-in-out;
		-o-transition: .35s ease-in-out;
		transition: .35s ease-in-out
	}

	.mb-3,
	.my-3 {
		color: #fff
	}

	.stickyForm ::-webkit-input-placeholder {
		color: #fff
	}

	.stickyForm.active {
		bottom: -379px;
		background: rgba(0, 0, 0, .3);
		-webkit-transition: .35s ease-in-out;
		-o-transition: .35s ease-in-out;
		transition: .35s ease-in-out
	}

	.stickyForm.active .btn {
		color: #f4c60e;
		width: 50%;
		height: 40px;
		padding: 0;
		left: 50%;
		cursor: pointer;
		top: -40px;
		background-color: #1f1f1fdb;
		border-radius: 0
	}

	.stickyForm .btn {
		background: rgba(0, 0, 0, .9);
		border: 0;
		border-radius: 5px 5px 0 0;
		bottom: auto;
		color: #fff;
		cursor: pointer;
		padding: 1px;
		position: absolute;
		top: -30px;
		left: 0;
		right: auto;
		margin: auto;
		height: 30px;
		font-size: 16px;
		-webkit-perspective: 100px;
		perspective: 100px;
		width: 20%;
		transform: unset;
		-webkit-transform: unset;
		-moz-transform: unset;
		-o-transform: unset;
		-ms-transform: unset
	}
}