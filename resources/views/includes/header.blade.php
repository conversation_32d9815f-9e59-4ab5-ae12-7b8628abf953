<section class="top-contact">
  <div class="container clearfix">
    <div class="top-contact--left pull-left">
      <span>
      <i class="fa fa-envelope" aria-hidden="true"></i>{{config('mail.team_email')}}
      </span>
      <span>
      <i class="fa fa-phone" aria-hidden="true"></i> <a href="tel:{{config('app.contact_no')}}">{{config('app.contact_no')}}</a>
      </span>
    </div>
    <div class="top-contact--right pull-right">
      <a href="#" target="_blank"><i class="fa fa-facebook" aria-hidden="true"></i></a>
      <a href="#" target="_blank"><i class="fa fa-instagram" aria-hidden="true"></i></a>
      <a href="#" target="_blank"><i class="fa fa-linkedin" aria-hidden="true"></i></a>
      <a href="#" target="_blank"><i class="fa fa-twitter" aria-hidden="true"></i></a>
    </div>
  </div>
</section>

<header class="header-mobile">
  <div class="container clearfix">
    <div class="logo-mob pull-left">
      <a href="/">
      <img alt="Interior Designers in Gurugram" src="{{asset('images/new_logo3.png')}}">
      </a>
    </div>
    <a class="menu-mobile__button">
    <i class="fa fa-bars"></i>
    </a>
    <nav class="menu-mobile hidden">
      <ul class="ul--no-style">
        <li>
          <i class="menu-mobile__more"></i>
          <a href="{{ config('app.url') }}">
          Home
          </a>
        </li>
        <li>
          <i class="menu-mobile__more"></i>
          <a href="/portfolios">
                  Portfolio
              </a>
          </li>
        <li>
          <i class="menu-mobile__more"></i>
          <a href="/reviews">
                  Reviews
              </a>
          </li>
        <li>
          <i class="fa fa-plus menu-mobile__more"></i>
          <a href="#">
                  Services <span class="caret"></span>
              </a>

          <ul class="ul--no-style hidden">
              @foreach($menuServices as $service)
                  @if($service->hasChildren() && $service->activeChildren()->count() > 0)
                      <li class="live-1" id="check-container">
                          <a href="{{ url('services/' . ($service->category ? $service->category->name : $service->seo_url) . ($service->category ? '/' . $service->seo_url : '')) }}" target="_blank">
                              {{ $service->title }} <span class="caret"></span></a>
                          <ul class="ul--no-style hidden">
                              @foreach($service->activeChildren as $childService)
                                  <li class="live check-container-1">
                                      <a href="{{ url('services/' . ($service->category ? $service->category->name : ($childService->category ? $childService->category->name : '')) . '/' . $childService->seo_url) }}">{{ $childService->title }}</a>
                                  </li>
                              @endforeach
                          </ul>
                      </li>
                  @else
                      <li class="live-1" id="check-container-01">
                          <a href="{{ url('services/' . ($service->category ? $service->category->name : $service->seo_url) . ($service->category ? '/' . $service->seo_url : '')) }}">
                              {{ $service->title }} <span class="caret"></span></a>
                      </li>
                  @endif
              @endforeach
          </ul>
          </li>
        <li>
          <i class="menu-mobile__more"></i>
          <a href="/about-us">
          About Us
          </a>
        </li>
        {{-- <li>
          <i class="menu-mobile__more"></i>
          <a href="blog">
          Blog
          </a>
        </li> --}}

        <li>
          <i class="fa fa-plus menu-mobile__more"></i>
          <a href="#">Cities<span class="caret"></span></a>
          <ul class="ul--no-style hidden">
            @if(isset($cities) && $cities->count() > 0)
                @foreach($cities as $city)
                    <li class="live-1" id="check-container">
                        <a href="{{ route('interior.cities.detail', $city->seo_url) }}" target="_blank">{{ $city->name }}<span class="caret"></span></a>
                    </li>
                @endforeach
            @else
                <li class="live-1" id="check-container">
                    <a href="#">No cities available</a>
                </li>
            @endif
          </ul>
        </li>
        <li>
          <a href="/contact-us">
          Contact Us
          </a>
        </li>
        @auth
        <li>
          <a href="{{ route('home') }}">
          Admin Dashboard
          </a>
        </li>
        @endauth

        <li>
          <a href="https://wa.me/91{{config('app.contact_no')}}?text=I%20would%20like%20to%20do%20interiors%20to%20my%20house!" target="_blank" class="click-on-2wp">
              <i class="fa fa-whatsapp wpfont"></i>
          </a>
          </li>
        <li class="li-has-sub">
          <a href="#" data-toggle="modal" data-target="#serviceTypeModal" class="click-on-2">
            Get Free Estimate
          </a>
        </li>
      </ul>
    </nav>
  </div>
</header>


<section class="slide">
  <nav class="navbar navbar-default navbar-sticky bootsnav">
    <!-- Header Desktop -->
    <header class="header-desktop header3">
      <div class="header-wrap clearfix">
        <div class="logo pull-left">
          <a href="/">
            <!-- The Eminence Space -->
            <img alt="Interior Designers" src="{{asset('images/new_logo3.png')}}">
          </a>
        </div>
        <nav class="menu-desktop menu-desktop--show pull-right">
          <ul class="ul--inline ul--no-style">
            <li class="li-has-sub">
              <a href="/">
              Home
              </a>
            </li>
            <li class="li-has-sub">
              <a href="/portfolios" target="_blank">
                Portfolio
              </a>
              </li>
            <li class="li-has-sub">
              <a href="/reviews" target="_blank">
              Reviews
              </a>
            </li>
             <li class="dropdown">
              <a href="#" class="dropdown-toggle" id="menu11" data-toggle="dropdown"> Services </a>

              <ul class="dropdown-menu">
                  @foreach($menuServices as $service)
                      @if($service->hasChildren() && $service->activeChildren()->count() > 0)
                          <li class="dropdown-submenu">
                              <a class="test" tabindex="-1" href="{{ url('services/' . ($service->category ? $service->category->name : $service->seo_url) . ($service->category ? '/' . $service->seo_url : '')) }}" target="_blank">{{ $service->title }} <span class="caret"></span></a>
                              <ul class="dropdown-menu-1">
                                  @foreach($service->activeChildren as $childService)
                                      <li><a tabindex="-1" href="{{ url('services/' . ($service->category ? $service->category->name : ($childService->category ? $childService->category->name : '')) . '/' . $childService->seo_url) }}" target="_blank">{{ $childService->title }}</a></li>
                                  @endforeach
                              </ul>
                          </li>
                      @else
                          <li class="live-1" id="check-container-01">
                              <a href="{{ url('services/' . ($service->category ? $service->category->name : $service->seo_url) . ($service->category ? '/' . $service->seo_url : '')) }}" target="_blank">{{ $service->title }} <span class="caret"></span></a>
                          </li>
                      @endif
                  @endforeach
              </ul>
              </li>

            <li>
              <a href="/contact-us" target="_blank">Contact Us</a>
            </li>
            @auth
            <li>
              <a href="{{ route('home') }}">Admin Dashboard</a>
            </li>
            @endauth
            <li>
              <i class="menu-mobile__more"></i>
              <a href="/about-us" target="_blank">About Us</a>
            </li>
            <!-- <li class="li-has-sub">
              <a href="blog" target="_blank">Blog</a>
              </li> -->
             <li class="dropdown">
              <a href="#" class="dropdown-toggle" id="menu11" data-toggle="dropdown"> Cities</a>
              <div class="dropdown-menu customwidth">
                  <div class="row">
                      @if(isset($cities) && $cities->count() > 0)
                          <div class="col-md-6">
                              <ul class="menu-list">
                                  @foreach($cities as $city)
                                      <li class="dropdown-submenu">
                                          <a class="test" tabindex="-1" href="{{ route('interior.cities.detail', $city->seo_url) }}" target="_blank">{{ $city->name }} <span class="caret"></span></a>
                                      </li>
                                  @endforeach
                              </ul>
                          </div>
                      @else
                          <div class="col-md-6">
                              <ul class="menu-list">
                                  <li>No cities available</li>
                              </ul>
                          </div>
                      @endif
                  </div>
              </div>
              </li>

            <li>
              <a href="https://wa.me/91{{config('app.contact_no')}}?text=I%20would%20like%20to%20do%20interiors%20to%20my%20house!" target="_blank" class="link click-onwp">
                  <i class="fa fa-whatsapp wpfont"></i>
              </a>
              </li>
            <li class="li-has-sub">
              <a href="#" data-toggle="modal" data-target="#serviceTypeModal" class="click-on">
                Get Free Estimate
              </a>
            </li>
          </ul>
        </nav>
      </div>
    </header>
  </nav>
</section>