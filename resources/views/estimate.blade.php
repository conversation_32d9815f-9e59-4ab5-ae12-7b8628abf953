@extends('layouts.masterlayout')

@section('onPageCSS')
<style>
    #successModal .modal-content {
        border-radius: 10px;
        border: none;
    }

    #successModal .modal-header {
        border-bottom: none;
        padding-bottom: 0;
    }

    #successModal .modal-footer {
        border-top: none;
        padding-top: 0;
    }

    #successModal .fa-check-circle {
        color: #28a745;
        animation: scale-in 0.5s ease-out;
    }

    @keyframes scale-in {
        0% {
            transform: scale(0);
            opacity: 0;
        }
        100% {
            transform: scale(1);
            opacity: 1;
        }
    }

    #successModal p {
        font-size: 18px;
        margin-top: 15px;
    }

    #successModal .btn-primary {
        background-color: #2A2B51;
        border-color: #2A2B51;
    }

    #successModal .btn-primary:hover {
        background-color: #595875;
        border-color: #595875;
    }

    #successModal #successMessage {
        color: black;
    }
</style>
@endsection

@section('content')
<section class='sections'>
    <div class="container">
        <div class="row store">
            <div class="col-md-5 mt-10 custom_heading">
                <div class="content-div1 mt-5">
                    <h1>
                        <span class='content-color'>Home {{$type}} Price Calculator</span><br/>
                    </h1>
                    <h3><span class='content-red'>Get the estimated cost for your dream home</span><br/>
                    </h3>
                </div>
                {{-- <div class="content-div01 mt-5">
                    <h4>
                        <span class='content-color'>Luxury Interior Designers</span><br/>
                        <span class='content-red'>Unbeatable Quality, Reasonable Price</span>
                    </h4>
                </div> --}}
            </div>
            <div class="col-md-2"></div>
            <div class="col-md-5" id="formSectionContainer">
                <div class="form-div" id="firstFormSection">
                    <div class="card text-center">
                        <form id="quick-forms" method="POST" action="{{ route('submit.enquiry') }}" name="myForm">
                            @csrf
                            <div class="need-query">
                                @if(session('error'))
                                    <p class="text-center" style="color:red">
                                        {{ session('error') }}
                                    </p>
                                @endif
                                @if($errors->any())
                                    <p class="text-center" style="color:red">
                                        @foreach($errors->all() as $error)
                                            {{ $error }}<br>
                                        @endforeach
                                    </p>
                                @endif
                            </div>
                            <!-- Hidden Inputs -->
                            <div class="quick-form">
                                <input type="hidden" name="url" id="url" value="{{url()->current()}}">
                                <input type="hidden" name="utm_source" id="utm_source" value="">
                                <input type="hidden" name="utm_medium" id="utm_medium" value="">
                                <input type="hidden" name="utm_campaign" id="utm_campaign" value="">
                                <input type="hidden" name="utm_term" id="utm_term" value="">
                                <!-- Default values for fields that are in the enquire form but not in the estimate form -->
                                <input type="hidden" name="propertylocation" value="Not Specified">
                                <input type="hidden" name="project-needs" value="Not Specified">
                            </div>
                            <!-- Step 1 -->
                            <div class="col-lg-12" id="formCard">
                                <div class="form-card">
                                    <h2 class="form-title">Now Get Premium & Luxury Home {{$type}} @ Reasonable Price!</h2>
                                    <!--<p>Start Your Interior Journey</p>-->
                                    <div class="form-group">
                                        <input type="text" class="form-control" id="clientname" name="clientname" placeholder=" " autocomplete="off" required>
                                        <label for="clientname" class="form-label">Name</label>
                                    </div>
                                    <div class="form-group">
                                        <input type="text" maxlength="10" name="phone" id="cphone" placeholder=" " autocomplete="off" class="form-control" required > {{-- pattern="[0-9]{10,13}" --}}
                                        <label for="cphone" class="form-label">Phone Number</label>
                                    </div>
                                    <div class="form-group">
                                        <input type="email" name="email" id="cemail" placeholder=" " autocomplete="off" class="form-control" required>
                                        <label for="cemail" class="form-label">Email</label>
                                    </div>
                                    <input type="hidden" name="project-needs" value="{{$type}}">
                                    <button type="submit" class="btn-next-red w-100 ">Submit</button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<div class="container">
    <p align="justify">
        <b>{{config('app.name')}}: Crafting Dreams, Building Homes</b></br>
        At {{config('app.name')}}, we don’t just design interiors — we transform houses into dream homes. Trusted by over {{config('app.projects_delivered')}} delighted homeowners, we are a leading name in premium and luxury end-to-end home interiors and construction services. From stunning interiors to solid foundations, we bring every aspect of your vision to life — beautifully, seamlessly, and affordably.
        Our team of {{config('app.team_size')}}+ expert designers and construction specialists combines innovation with functionality, tailoring each space to reflect your personality and lifestyle. Whether you’re building from the ground up or giving your space a stylish makeover, we handle it all — from concept and design to execution and handover.

        </br></br>
        ✨ Why Choose {{config('app.name')}}?</br>
        - <b>Design to Delivery: </b> End-to-end interior and construction solutions under one roof.</br>
        - <b>Fair & Transparent Pricing:</b> No hidden surprises — just honest value.</br>
        - <b>On-Time Delivery:</b> We respect your time and deliver with precision.</br>
        - <b>Creative Minds at Work:</b> {{config('app.team_size')}}+ professionals turning ideas into reality.</br>
        Whether your style leans modern, classic, or something uniquely yours, we blend aesthetics with practicality to create spaces that truly feel like you. With meticulous attention to detail, reliable project management, and a commitment to excellence, {{config('app.name')}} ensures a stress-free journey from blueprint to reality.</br>
    </p>
</div>
@endsection

@section('onPageJS')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Set the URL value
        document.getElementById('url').value = window.location.href;

        // Get UTM parameters from URL if present
        const urlParams = new URLSearchParams(window.location.search);
        if (urlParams.has('utm_source')) {
            document.getElementById('utm_source').value = urlParams.get('utm_source');
        }
        if (urlParams.has('utm_medium')) {
            document.getElementById('utm_medium').value = urlParams.get('utm_medium');
        }
        if (urlParams.has('utm_campaign')) {
            document.getElementById('utm_campaign').value = urlParams.get('utm_campaign');
        }
        if (urlParams.has('utm_term')) {
            document.getElementById('utm_term').value = urlParams.get('utm_term');
        }

        // Check if there's a success message in the session
        @if(session('success'))
            // Reset the form
            document.getElementById('quick-forms').reset();

            // Set the success message in the modal
            document.getElementById('successMessage').textContent = "{{ session('success') }}";

            // Show the modal with a slight delay for better UX
            setTimeout(function() {
                $('#successModal').modal('show');
            }, 300);

            // Add event listener to close button to redirect after closing
            $('#successModal').on('hidden.bs.modal', function () {
                // Optional: You can add a redirect here if needed
                // window.location.href = '/';
            });
        @endif
    });
</script>
@endsection
