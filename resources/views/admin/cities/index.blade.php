@extends('layouts.app')

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <span>{{ __('City Management') }}</span>
                    <a href="{{ route('city.create') }}" class="btn btn-primary btn-sm">Add New City</a>
                </div>

                <div class="card-body">
                    @if (session('success'))
                        <div class="alert alert-success" role="alert">
                            {{ session('success') }}
                        </div>
                    @endif

                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Banner</th>
                                    <th>Name</th>
                                    <th>SEO URL</th>
                                    <th>Priority</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse ($cities as $city)
                                    <tr>
                                        <td>{{ $city->id }}</td>
                                        <td>
                                            @if($city->banner)
                                                <img src="{{ asset('images/cities/' . $city->banner) }}" alt="{{ $city->name }}" style="max-width: 100px; max-height: 60px;">
                                            @else
                                                No Banner
                                            @endif
                                        </td>
                                        <td>{{ $city->name }}</td>
                                        <td>{{ $city->seo_url }}</td>
                                        <td>
                                            <span class="badge bg-info">{{ $city->priority }}</span>
                                        </td>
                                        <td>
                                            <span class="badge {{ $city->status == 'active' ? 'bg-success' : 'bg-danger' }}">
                                                {{ ucfirst($city->status) }}
                                            </span>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="{{ route('city.show', $city->id) }}" class="btn btn-info btn-sm">View</a>
                                                <a href="{{ route('city.edit', $city->id) }}" class="btn btn-primary btn-sm">Edit</a>
                                                <form action="{{ route('city.destroy', $city->id) }}" method="POST" class="d-inline" onsubmit="return confirm('Are you sure you want to delete this city?');">
                                                    @csrf
                                                    @method('DELETE')
                                                    <button type="submit" class="btn btn-danger btn-sm">Delete</button>
                                                </form>
                                            </div>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="7" class="text-center">No cities found.</td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>

                    <div class="d-flex justify-content-center mt-4">
                        {{ $cities->links() }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
